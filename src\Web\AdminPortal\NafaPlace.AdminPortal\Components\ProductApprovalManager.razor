@using System.Text.Json
@inject HttpClient HttpClient
@inject IJSRuntime JSRuntime

<div class="product-approval-manager">
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0">
                <i class="fas fa-clipboard-check me-2"></i>
                Gestion des Approbations de Produits
            </h5>
            <div class="d-flex gap-2">
                <button class="btn btn-outline-primary btn-sm" @onclick="RefreshData">
                    <i class="fas fa-sync-alt me-1"></i>
                    Actualiser
                </button>
                <button class="btn btn-outline-success btn-sm" @onclick="ShowBulkApprovalModal" disabled="@(!_selectedProducts.Any())">
                    <i class="fas fa-check-double me-1"></i>
                    Approbation groupée (@_selectedProducts.Count)
                </button>
            </div>
        </div>

        <div class="card-body">
            @if (_isLoading)
            {
                <div class="text-center py-4">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Chargement...</span>
                    </div>
                    <p class="mt-2">Chargement des produits en attente...</p>
                </div>
            }
            else
            {
                <!-- Statistiques rapides -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card bg-warning text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6 class="card-title">En attente</h6>
                                        <h3 class="mb-0">@_stats.PendingApprovals</h3>
                                    </div>
                                    <i class="fas fa-clock fa-2x opacity-75"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-success text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6 class="card-title">Approuvés</h6>
                                        <h3 class="mb-0">@_stats.ApprovedProducts</h3>
                                    </div>
                                    <i class="fas fa-check-circle fa-2x opacity-75"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-danger text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6 class="card-title">Rejetés</h6>
                                        <h3 class="mb-0">@_stats.RejectedProducts</h3>
                                    </div>
                                    <i class="fas fa-times-circle fa-2x opacity-75"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-info text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6 class="card-title">Taux d'approbation</h6>
                                        <h3 class="mb-0">@(_stats.ApprovalRate.ToString("F1"))%</h3>
                                    </div>
                                    <i class="fas fa-chart-line fa-2x opacity-75"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Filtres -->
                <div class="row mb-3">
                    <div class="col-md-4">
                        <select class="form-select" @bind="_selectedStatus" @bind:after="FilterProducts">
                            <option value="">Tous les statuts</option>
                            <option value="Pending">En attente</option>
                            <option value="Approved">Approuvés</option>
                            <option value="Rejected">Rejetés</option>
                        </select>
                    </div>
                    <div class="col-md-4">
                        <input type="text" class="form-control" placeholder="Rechercher un produit..." 
                               @bind="_searchTerm" @bind:after="FilterProducts">
                    </div>
                    <div class="col-md-4">
                        <select class="form-select" @bind="_selectedSeller" @bind:after="FilterProducts">
                            <option value="">Tous les vendeurs</option>
                            @foreach (var seller in _sellers)
                            {
                                <option value="@seller.Id">@seller.Name</option>
                            }
                        </select>
                    </div>
                </div>

                <!-- Liste des produits -->
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>
                                    <input type="checkbox" class="form-check-input" 
                                           @onchange="ToggleSelectAll" 
                                           checked="@(_selectedProducts.Count == _filteredProducts.Count && _filteredProducts.Any())">
                                </th>
                                <th>Image</th>
                                <th>Produit</th>
                                <th>Vendeur</th>
                                <th>Prix</th>
                                <th>Statut</th>
                                <th>Soumis le</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var product in _filteredProducts)
                            {
                                <tr class="@(GetRowClass(product.Status))">
                                    <td>
                                        <input type="checkbox" class="form-check-input" 
                                               @onchange="(e) => ToggleProductSelection(product.ProductId, (bool)e.Value!)"
                                               checked="@_selectedProducts.Contains(product.ProductId)">
                                    </td>
                                    <td>
                                        @if (!string.IsNullOrEmpty(product.ImageUrl))
                                        {
                                            <img src="@product.ImageUrl" alt="@product.ProductName" 
                                                 class="product-thumbnail" style="width: 50px; height: 50px; object-fit: cover; border-radius: 4px;">
                                        }
                                        else
                                        {
                                            <div class="bg-light d-flex align-items-center justify-content-center" 
                                                 style="width: 50px; height: 50px; border-radius: 4px;">
                                                <i class="fas fa-image text-muted"></i>
                                            </div>
                                        }
                                    </td>
                                    <td>
                                        <div>
                                            <strong>@product.ProductName</strong>
                                            <br>
                                            <small class="text-muted">@product.Category</small>
                                        </div>
                                    </td>
                                    <td>@product.SellerName</td>
                                    <td>@product.Price.ToString("C", new System.Globalization.CultureInfo("fr-GN"))</td>
                                    <td>
                                        <span class="badge @GetStatusBadgeClass(product.Status)">
                                            @GetStatusDisplayName(product.Status)
                                        </span>
                                    </td>
                                    <td>@product.SubmittedAt.ToString("dd/MM/yyyy HH:mm")</td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <button class="btn btn-sm btn-outline-primary" 
                                                    @onclick="() => ViewProductDetails(product.ProductId)">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            
                                            @if (product.Status.ToString() == "Pending")
                                            {
                                                <button class="btn btn-sm btn-success" 
                                                        @onclick="() => ApproveProduct(product.ProductId)">
                                                    <i class="fas fa-check"></i>
                                                </button>
                                                <button class="btn btn-sm btn-danger" 
                                                        @onclick="() => ShowRejectModal(product.ProductId, product.ProductName)">
                                                    <i class="fas fa-times"></i>
                                                </button>
                                            }
                                        </div>
                                    </td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>

                @if (!_filteredProducts.Any())
                {
                    <div class="text-center py-4">
                        <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                        <p class="text-muted">Aucun produit trouvé avec les critères sélectionnés.</p>
                    </div>
                }
            }
        </div>
    </div>
</div>

<!-- Modal de rejet -->
@if (_showRejectModal)
{
    <div class="modal fade show" style="display: block;" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Rejeter le produit</h5>
                    <button type="button" class="btn-close" @onclick="CloseRejectModal"></button>
                </div>
                <div class="modal-body">
                    <p>Vous êtes sur le point de rejeter le produit : <strong>@_selectedProductName</strong></p>
                    <div class="mb-3">
                        <label class="form-label">Raison du rejet *</label>
                        <textarea class="form-control" rows="3" @bind="_rejectionReason" 
                                  placeholder="Expliquez pourquoi ce produit est rejeté..."></textarea>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Notes additionnelles</label>
                        <textarea class="form-control" rows="2" @bind="_rejectionNotes" 
                                  placeholder="Notes internes (optionnel)..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" @onclick="CloseRejectModal">Annuler</button>
                    <button type="button" class="btn btn-danger" @onclick="ConfirmRejectProduct" 
                            disabled="@(string.IsNullOrWhiteSpace(_rejectionReason))">
                        <i class="fas fa-times me-1"></i>
                        Rejeter le produit
                    </button>
                </div>
            </div>
        </div>
    </div>
    <div class="modal-backdrop fade show"></div>
}

@code {
    [Parameter] public EventCallback<string> OnStatusChanged { get; set; }

    private bool _isLoading = true;
    private List<ProductApprovalDto> _products = new();
    private List<ProductApprovalDto> _filteredProducts = new();
    private List<SellerDto> _sellers = new();
    private ProductApprovalStatsDto _stats = new();
    private HashSet<int> _selectedProducts = new();

    // Filtres
    private string _selectedStatus = "";
    private string _searchTerm = "";
    private string _selectedSeller = "";

    // Modal de rejet
    private bool _showRejectModal = false;
    private int _selectedProductId = 0;
    private string _selectedProductName = "";
    private string _rejectionReason = "";
    private string _rejectionNotes = "";

    protected override async Task OnInitializedAsync()
    {
        await LoadData();
    }

    private async Task LoadData()
    {
        _isLoading = true;
        try
        {
            await Task.WhenAll(
                LoadProducts(),
                LoadSellers(),
                LoadStats()
            );
            
            FilterProducts();
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("showToast", "Erreur lors du chargement des données", "error");
            Console.WriteLine($"Erreur: {ex.Message}");
        }
        finally
        {
            _isLoading = false;
        }
    }

    private async Task LoadProducts()
    {
        try
        {
            var response = await HttpClient.GetAsync("/api/products/approvals/pending");
            if (response.IsSuccessStatusCode)
            {
                var json = await response.Content.ReadAsStringAsync();
                _products = JsonSerializer.Deserialize<List<ProductApprovalDto>>(json, 
                    new JsonSerializerOptions { PropertyNameCaseInsensitive = true }) ?? new();
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur chargement produits: {ex.Message}");
        }
    }

    private async Task LoadSellers()
    {
        try
        {
            var response = await HttpClient.GetAsync("/api/sellers");
            if (response.IsSuccessStatusCode)
            {
                var json = await response.Content.ReadAsStringAsync();
                _sellers = JsonSerializer.Deserialize<List<SellerDto>>(json, 
                    new JsonSerializerOptions { PropertyNameCaseInsensitive = true }) ?? new();
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur chargement vendeurs: {ex.Message}");
        }
    }

    private async Task LoadStats()
    {
        try
        {
            var response = await HttpClient.GetAsync("/api/products/approvals/stats");
            if (response.IsSuccessStatusCode)
            {
                var json = await response.Content.ReadAsStringAsync();
                _stats = JsonSerializer.Deserialize<ProductApprovalStatsDto>(json, 
                    new JsonSerializerOptions { PropertyNameCaseInsensitive = true }) ?? new();
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur chargement statistiques: {ex.Message}");
        }
    }

    private void FilterProducts()
    {
        _filteredProducts = _products.Where(p =>
            (string.IsNullOrEmpty(_selectedStatus) || p.Status.ToString() == _selectedStatus) &&
            (string.IsNullOrEmpty(_searchTerm) || p.ProductName.Contains(_searchTerm, StringComparison.OrdinalIgnoreCase)) &&
            (string.IsNullOrEmpty(_selectedSeller) || p.SellerId.ToString() == _selectedSeller)
        ).ToList();

        // Réinitialiser la sélection si nécessaire
        _selectedProducts.RemoveWhere(id => !_filteredProducts.Any(p => p.ProductId == id));
    }

    private async Task ApproveProduct(int productId)
    {
        try
        {
            var response = await HttpClient.PutAsync($"/api/products/{productId}/approve", null);
            if (response.IsSuccessStatusCode)
            {
                await JSRuntime.InvokeVoidAsync("showToast", "Produit approuvé avec succès", "success");
                await RefreshData();
                await OnStatusChanged.InvokeAsync("approved");
            }
            else
            {
                await JSRuntime.InvokeVoidAsync("showToast", "Erreur lors de l'approbation", "error");
            }
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("showToast", "Erreur lors de l'approbation", "error");
            Console.WriteLine($"Erreur approbation: {ex.Message}");
        }
    }

    private void ShowRejectModal(int productId, string productName)
    {
        _selectedProductId = productId;
        _selectedProductName = productName;
        _rejectionReason = "";
        _rejectionNotes = "";
        _showRejectModal = true;
    }

    private void CloseRejectModal()
    {
        _showRejectModal = false;
        _selectedProductId = 0;
        _selectedProductName = "";
        _rejectionReason = "";
        _rejectionNotes = "";
    }

    private async Task ConfirmRejectProduct()
    {
        try
        {
            var request = new
            {
                RejectionReason = _rejectionReason,
                Notes = _rejectionNotes
            };

            var response = await HttpClient.PutAsJsonAsync($"/api/products/{_selectedProductId}/reject", request);
            if (response.IsSuccessStatusCode)
            {
                await JSRuntime.InvokeVoidAsync("showToast", "Produit rejeté avec succès", "success");
                CloseRejectModal();
                await RefreshData();
                await OnStatusChanged.InvokeAsync("rejected");
            }
            else
            {
                await JSRuntime.InvokeVoidAsync("showToast", "Erreur lors du rejet", "error");
            }
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("showToast", "Erreur lors du rejet", "error");
            Console.WriteLine($"Erreur rejet: {ex.Message}");
        }
    }

    private void ToggleProductSelection(int productId, bool isSelected)
    {
        if (isSelected)
            _selectedProducts.Add(productId);
        else
            _selectedProducts.Remove(productId);
    }

    private void ToggleSelectAll(ChangeEventArgs e)
    {
        var selectAll = (bool)e.Value!;
        if (selectAll)
        {
            _selectedProducts = _filteredProducts.Select(p => p.ProductId).ToHashSet();
        }
        else
        {
            _selectedProducts.Clear();
        }
    }

    private async Task RefreshData()
    {
        await LoadData();
    }

    private void ViewProductDetails(int productId)
    {
        // Naviguer vers les détails du produit
        // NavigationManager.NavigateTo($"/admin/products/{productId}");
    }

    private void ShowBulkApprovalModal()
    {
        // Afficher le modal d'approbation groupée
    }

    // Méthodes utilitaires
    private string GetRowClass(object status)
    {
        return status.ToString() switch
        {
            "Pending" => "table-warning",
            "Approved" => "table-success",
            "Rejected" => "table-danger",
            _ => ""
        };
    }

    private string GetStatusBadgeClass(object status)
    {
        return status.ToString() switch
        {
            "Pending" => "bg-warning",
            "Approved" => "bg-success",
            "Rejected" => "bg-danger",
            _ => "bg-secondary"
        };
    }

    private string GetStatusDisplayName(object status)
    {
        return status.ToString() switch
        {
            "Pending" => "En attente",
            "Approved" => "Approuvé",
            "Rejected" => "Rejeté",
            _ => status.ToString()
        };
    }

    // DTOs
    public class ProductApprovalDto
    {
        public int ProductId { get; set; }
        public string ProductName { get; set; } = "";
        public string SellerName { get; set; } = "";
        public int SellerId { get; set; }
        public object Status { get; set; } = "";
        public DateTime SubmittedAt { get; set; }
        public string? ImageUrl { get; set; }
        public decimal Price { get; set; }
        public string Category { get; set; } = "";
    }

    public class ProductApprovalStatsDto
    {
        public int PendingApprovals { get; set; }
        public int ApprovedProducts { get; set; }
        public int RejectedProducts { get; set; }
        public double ApprovalRate { get; set; }
    }

    public class SellerDto
    {
        public int Id { get; set; }
        public string Name { get; set; } = "";
    }
}

<style>
    .product-approval-manager .table th {
        background-color: #f8f9fa;
        border-top: none;
        font-weight: 600;
    }

    .product-approval-manager .btn-group .btn {
        border-radius: 0.25rem;
        margin-right: 2px;
    }

    .product-approval-manager .card {
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    }

    .product-approval-manager .modal-backdrop {
        z-index: 1040;
    }

    .product-approval-manager .modal {
        z-index: 1050;
    }
</style>
