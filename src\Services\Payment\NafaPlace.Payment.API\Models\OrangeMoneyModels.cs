namespace NafaPlace.Payment.API.Models
{
    public enum OrangeMoneyStatus
    {
        Pending,
        Success,
        Failed,
        Expired,
        Cancelled
    }

    public class OrangeMoneyCallback
    {
        public string TransactionId { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
        public string Signature { get; set; } = string.Empty;
        public string Amount { get; set; } = string.Empty;
        public string Currency { get; set; } = string.Empty;
        public DateTime Timestamp { get; set; }
    }

    public class OrangeMoneyApiResponse
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public object? Data { get; set; }
        public string ErrorCode { get; set; } = string.Empty;
    }

    public class OrangeMoneyErrorResponse
    {
        public string ErrorCode { get; set; } = string.Empty;
        public string ErrorMessage { get; set; } = string.Empty;
        public string Details { get; set; } = string.Empty;
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    }
}
