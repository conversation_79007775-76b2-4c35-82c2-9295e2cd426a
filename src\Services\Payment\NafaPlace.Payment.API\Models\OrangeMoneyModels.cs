using System.Text.Json.Serialization;

namespace NafaPlace.Payment.API.Models
{
    // Modèles de requête
    public class OrangeMoneyPaymentRequest
    {
        public string OrderId { get; set; } = string.Empty;
        public decimal Amount { get; set; }
        public string Currency { get; set; } = "GNF";
        public string PhoneNumber { get; set; } = string.Empty;
        public string? CustomerEmail { get; set; }
        public string? CustomerFirstName { get; set; }
        public string? CustomerLastName { get; set; }
        public string? Description { get; set; }
        public Dictionary<string, string>? Metadata { get; set; }
    }

    // Modèles de réponse
    public class OrangeMoneyPaymentResponse
    {
        public string TransactionId { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public string PaymentUrl { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; }
        public string? QrCode { get; set; }
        public int? ExpiresInMinutes { get; set; }
    }

    public class OrangeMoneyStatusResponse
    {
        public string TransactionId { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
        public decimal Amount { get; set; }
        public string Currency { get; set; } = string.Empty;
        public string PhoneNumber { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; }
        public DateTime? CompletedAt { get; set; }
        public string? FailureReason { get; set; }
        public Dictionary<string, string>? Metadata { get; set; }
    }

    // Modèles pour l'API Orange Money réelle
    public class OrangeMoneyApiResponse
    {
        [JsonPropertyName("status")]
        public string Status { get; set; } = string.Empty;

        [JsonPropertyName("message")]
        public string Message { get; set; } = string.Empty;

        [JsonPropertyName("transaction_id")]
        public string? TransactionId { get; set; }

        [JsonPropertyName("payment_url")]
        public string? PaymentUrl { get; set; }

        [JsonPropertyName("qr_code")]
        public string? QrCode { get; set; }

        [JsonPropertyName("expires_at")]
        public DateTime? ExpiresAt { get; set; }

        [JsonPropertyName("data")]
        public OrangeMoneyApiData? Data { get; set; }
    }

    public class OrangeMoneyApiData
    {
        [JsonPropertyName("order_id")]
        public string? OrderId { get; set; }

        [JsonPropertyName("amount")]
        public decimal Amount { get; set; }

        [JsonPropertyName("currency")]
        public string Currency { get; set; } = string.Empty;

        [JsonPropertyName("customer_msisdn")]
        public string? CustomerMsisdn { get; set; }
    }

    public class OrangeMoneyErrorResponse
    {
        [JsonPropertyName("error")]
        public string Error { get; set; } = string.Empty;

        [JsonPropertyName("message")]
        public string Message { get; set; } = string.Empty;

        [JsonPropertyName("code")]
        public string? Code { get; set; }

        [JsonPropertyName("details")]
        public Dictionary<string, string>? Details { get; set; }
    }

    // Modèle pour les callbacks/webhooks
    public class OrangeMoneyCallback
    {
        [JsonPropertyName("transaction_id")]
        public string TransactionId { get; set; } = string.Empty;

        [JsonPropertyName("order_id")]
        public string OrderId { get; set; } = string.Empty;

        [JsonPropertyName("status")]
        public string Status { get; set; } = string.Empty;

        [JsonPropertyName("amount")]
        public decimal Amount { get; set; }

        [JsonPropertyName("currency")]
        public string Currency { get; set; } = string.Empty;

        [JsonPropertyName("customer_msisdn")]
        public string CustomerMsisdn { get; set; } = string.Empty;

        [JsonPropertyName("timestamp")]
        public DateTime Timestamp { get; set; }

        [JsonPropertyName("signature")]
        public string Signature { get; set; } = string.Empty;

        [JsonPropertyName("metadata")]
        public Dictionary<string, string>? Metadata { get; set; }
    }

    // Énumérations pour les statuts
    public static class OrangeMoneyStatus
    {
        public const string Pending = "PENDING";
        public const string Success = "SUCCESS";
        public const string Failed = "FAILED";
        public const string Cancelled = "CANCELLED";
        public const string Expired = "EXPIRED";
        public const string Processing = "PROCESSING";
    }

    // Modèle pour la vérification de statut
    public class OrangeMoneyStatusRequest
    {
        public string TransactionId { get; set; } = string.Empty;
        public string? OrderId { get; set; }
    }

    // Modèle pour l'annulation de paiement
    public class OrangeMoneyRefundRequest
    {
        public string TransactionId { get; set; } = string.Empty;
        public decimal? Amount { get; set; } // Si null, remboursement total
        public string Reason { get; set; } = string.Empty;
        public Dictionary<string, string>? Metadata { get; set; }
    }

    public class OrangeMoneyRefundResponse
    {
        public string RefundId { get; set; } = string.Empty;
        public string TransactionId { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
        public decimal Amount { get; set; }
        public string Currency { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; }
        public string Message { get; set; } = string.Empty;
    }
}
