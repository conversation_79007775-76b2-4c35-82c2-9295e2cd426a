using NafaPlace.Catalog.Domain.Enums;

namespace NafaPlace.Catalog.Application.DTOs.Product;

public class ProductApprovalDto
{
    public int ProductId { get; set; }
    public string ProductName { get; set; } = string.Empty;
    public string SellerName { get; set; } = string.Empty;
    public int SellerId { get; set; }
    public ProductApprovalStatus Status { get; set; }
    public DateTime SubmittedAt { get; set; }
    public DateTime? ApprovedAt { get; set; }
    public string? ApprovedBy { get; set; }
    public string? RejectionReason { get; set; }
    public string? ImageUrl { get; set; }
    public decimal Price { get; set; }
    public string Category { get; set; } = string.Empty;
    public string? Notes { get; set; }
    public int Priority { get; set; } = 0;
    public List<string> Tags { get; set; } = new();
}

public class ProductApprovalResult
{
    public bool Success { get; set; }
    public int ProductId { get; set; }
    public ProductApprovalStatus? NewStatus { get; set; }
    public string Message { get; set; } = string.Empty;
    public string? ErrorMessage { get; set; }
    public List<string> ValidationErrors { get; set; } = new();
    public Dictionary<string, object> AdditionalData { get; set; } = new();
}

public class ProductApprovalStatsDto
{
    public int TotalProducts { get; set; }
    public int PendingApprovals { get; set; }
    public int ApprovedProducts { get; set; }
    public int RejectedProducts { get; set; }
    public double AverageApprovalTimeHours { get; set; }
    public double ApprovalRate { get; set; }
    public Dictionary<string, int> ApprovalsByCategory { get; set; } = new();
    public Dictionary<string, int> ApprovalsBySeller { get; set; } = new();
    public List<ProductApprovalTrendDto> ApprovalTrends { get; set; } = new();
}

public class ProductApprovalTrendDto
{
    public DateTime Date { get; set; }
    public int Submitted { get; set; }
    public int Approved { get; set; }
    public int Rejected { get; set; }
}

public class ProductApprovalWorkflowDto
{
    public int ProductId { get; set; }
    public string ProductName { get; set; } = string.Empty;
    public ProductApprovalStatus CurrentStatus { get; set; }
    public List<ProductApprovalStepDto> Steps { get; set; } = new();
    public List<ProductApprovalActionDto> AvailableActions { get; set; } = new();
    public ProductApprovalRequirementsDto Requirements { get; set; } = new();
}

public class ProductApprovalStepDto
{
    public int StepNumber { get; set; }
    public string StepName { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public bool IsCompleted { get; set; }
    public bool IsActive { get; set; }
    public DateTime? CompletedAt { get; set; }
    public string? CompletedBy { get; set; }
    public string? Notes { get; set; }
}

public class ProductApprovalActionDto
{
    public string ActionType { get; set; } = string.Empty;
    public string ActionName { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public bool RequiresReason { get; set; }
    public bool RequiresNotes { get; set; }
    public List<string> RequiredPermissions { get; set; } = new();
}

public class ProductApprovalRequirementsDto
{
    public bool HasName { get; set; }
    public bool HasDescription { get; set; }
    public bool HasPrice { get; set; }
    public bool HasCategory { get; set; }
    public bool HasImages { get; set; }
    public bool HasStock { get; set; }
    public int MinimumImages { get; set; } = 1;
    public int MinimumDescriptionLength { get; set; } = 50;
    public List<string> MissingRequirements { get; set; } = new();
    public bool IsReadyForApproval { get; set; }
}

public class ProductValidationResult
{
    public bool IsValid { get; set; }
    public List<string> Errors { get; set; } = new();
    public List<string> Warnings { get; set; } = new();
    public Dictionary<string, object> ValidationData { get; set; } = new();
}

public class BulkApprovalRequest
{
    public List<int> ProductIds { get; set; } = new();
    public string Action { get; set; } = string.Empty; // "approve" or "reject"
    public string? Reason { get; set; }
    public string? Notes { get; set; }
    public string PerformedBy { get; set; } = string.Empty;
}

public class BulkApprovalResult
{
    public bool Success { get; set; }
    public int TotalProducts { get; set; }
    public int SuccessfulActions { get; set; }
    public int FailedActions { get; set; }
    public List<BulkApprovalItemResult> Results { get; set; } = new();
    public string? ErrorMessage { get; set; }
}

public class BulkApprovalItemResult
{
    public int ProductId { get; set; }
    public string ProductName { get; set; } = string.Empty;
    public bool Success { get; set; }
    public string? ErrorMessage { get; set; }
    public ProductApprovalStatus? NewStatus { get; set; }
}

// Requêtes pour les actions d'approbation
public class ApproveProductRequest
{
    public int ProductId { get; set; }
    public string ApprovedBy { get; set; } = string.Empty;
    public string? Notes { get; set; }
    public bool NotifySeller { get; set; } = true;
    public bool AutoActivate { get; set; } = true;
}

public class RejectProductRequest
{
    public int ProductId { get; set; }
    public string RejectedBy { get; set; } = string.Empty;
    public string RejectionReason { get; set; } = string.Empty;
    public string? Notes { get; set; }
    public bool NotifySeller { get; set; } = true;
    public bool AllowResubmission { get; set; } = true;
}

public class RequestChangesRequest
{
    public int ProductId { get; set; }
    public string RequestedBy { get; set; } = string.Empty;
    public string ChangeRequests { get; set; } = string.Empty;
    public List<string> RequiredChanges { get; set; } = new();
    public string? Notes { get; set; }
    public DateTime? Deadline { get; set; }
}

// Énumérations pour les actions d'approbation
public enum ProductApprovalAction
{
    SubmittedForApproval,
    Approved,
    Rejected,
    ChangesRequested,
    Resubmitted,
    AutoApproved,
    Escalated
}

public enum ProductApprovalPriority
{
    Low = 0,
    Normal = 1,
    High = 2,
    Urgent = 3
}

// Modèles pour les filtres et recherches
public class ProductApprovalFilterDto
{
    public ProductApprovalStatus? Status { get; set; }
    public int? SellerId { get; set; }
    public int? CategoryId { get; set; }
    public DateTime? SubmittedAfter { get; set; }
    public DateTime? SubmittedBefore { get; set; }
    public ProductApprovalPriority? Priority { get; set; }
    public string? SearchTerm { get; set; }
    public string? ApprovedBy { get; set; }
    public bool? HasImages { get; set; }
    public decimal? MinPrice { get; set; }
    public decimal? MaxPrice { get; set; }
    public int Page { get; set; } = 1;
    public int PageSize { get; set; } = 20;
    public string SortBy { get; set; } = "SubmittedAt";
    public string SortDirection { get; set; } = "asc";
}

public class ProductApprovalSearchResult
{
    public List<ProductApprovalDto> Products { get; set; } = new();
    public int TotalCount { get; set; }
    public int Page { get; set; }
    public int PageSize { get; set; }
    public int TotalPages { get; set; }
    public bool HasNextPage { get; set; }
    public bool HasPreviousPage { get; set; }
    public ProductApprovalFilterDto AppliedFilters { get; set; } = new();
}

// Configuration pour les règles d'approbation
public class ProductApprovalRulesDto
{
    public bool RequireManualApproval { get; set; } = true;
    public bool AutoApproveFromTrustedSellers { get; set; } = false;
    public List<int> TrustedSellerIds { get; set; } = new();
    public int MaxAutoApprovalPrice { get; set; } = 0;
    public List<string> RestrictedCategories { get; set; } = new();
    public int MinimumSellerRating { get; set; } = 0;
    public bool RequireImageModeration { get; set; } = true;
    public bool RequireContentModeration { get; set; } = true;
    public int MaxApprovalTimeHours { get; set; } = 72;
    public bool EscalateAfterTimeout { get; set; } = true;
}

// Modèle pour les notifications d'approbation
public class ProductApprovalNotificationDto
{
    public int ProductId { get; set; }
    public string ProductName { get; set; } = string.Empty;
    public string SellerName { get; set; } = string.Empty;
    public string SellerId { get; set; } = string.Empty;
    public ProductApprovalAction Action { get; set; }
    public string? Reason { get; set; }
    public string? Notes { get; set; }
    public string PerformedBy { get; set; } = string.Empty;
    public DateTime Timestamp { get; set; }
    public Dictionary<string, object> AdditionalData { get; set; } = new();
}
