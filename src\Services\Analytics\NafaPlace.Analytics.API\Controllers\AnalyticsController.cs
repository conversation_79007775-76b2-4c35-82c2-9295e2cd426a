using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using NafaPlace.Analytics.Application.Interfaces;
using NafaPlace.Analytics.Application.DTOs;

namespace NafaPlace.Analytics.API.Controllers;

[ApiController]
[Route("api/[controller]")]
[Authorize]
public class AnalyticsController : ControllerBase
{
    private readonly IAnalyticsService _analyticsService;
    private readonly ILogger<AnalyticsController> _logger;

    public AnalyticsController(
        IAnalyticsService analyticsService,
        ILogger<AnalyticsController> logger)
    {
        _analyticsService = analyticsService;
        _logger = logger;
    }

    /// <summary>
    /// Get dashboard statistics for sellers
    /// </summary>
    [HttpGet("seller/{sellerId}/dashboard")]
    public async Task<ActionResult<SellerDashboardDto>> GetSellerDashboard(
        int sellerId,
        [FromQuery] DateTime? startDate = null,
        [FromQuery] DateTime? endDate = null)
    {
        try
        {
            var request = new AnalyticsRequest
            {
                SellerId = sellerId,
                StartDate = startDate ?? DateTime.UtcNow.AddDays(-30),
                EndDate = endDate ?? DateTime.UtcNow
            };

            var dashboard = await _analyticsService.GetSellerDashboardAsync(request);
            return Ok(dashboard);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting seller dashboard for seller {SellerId}", sellerId);
            return StatusCode(500, new { error = "Internal server error" });
        }
    }

    /// <summary>
    /// Get admin dashboard statistics
    /// </summary>
    [HttpGet("admin/dashboard")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult<AdminDashboardDto>> GetAdminDashboard(
        [FromQuery] DateTime? startDate = null,
        [FromQuery] DateTime? endDate = null)
    {
        try
        {
            var request = new AnalyticsRequest
            {
                StartDate = startDate ?? DateTime.UtcNow.AddDays(-30),
                EndDate = endDate ?? DateTime.UtcNow
            };

            var dashboard = await _analyticsService.GetAdminDashboardAsync(request);
            return Ok(dashboard);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting admin dashboard");
            return StatusCode(500, new { error = "Internal server error" });
        }
    }

    /// <summary>
    /// Get sales analytics
    /// </summary>
    [HttpGet("sales")]
    public async Task<ActionResult<SalesAnalyticsDto>> GetSalesAnalytics(
        [FromQuery] int? sellerId = null,
        [FromQuery] DateTime? startDate = null,
        [FromQuery] DateTime? endDate = null,
        [FromQuery] string groupBy = "day")
    {
        try
        {
            var request = new AnalyticsRequest
            {
                SellerId = sellerId,
                StartDate = startDate ?? DateTime.UtcNow.AddDays(-30),
                EndDate = endDate ?? DateTime.UtcNow,
                GroupBy = groupBy
            };

            var analytics = await _analyticsService.GetSalesAnalyticsAsync(request);
            return Ok(analytics);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting sales analytics");
            return StatusCode(500, new { error = "Internal server error" });
        }
    }

    /// <summary>
    /// Get product performance analytics
    /// </summary>
    [HttpGet("products/performance")]
    public async Task<ActionResult<ProductPerformanceDto>> GetProductPerformance(
        [FromQuery] int? sellerId = null,
        [FromQuery] DateTime? startDate = null,
        [FromQuery] DateTime? endDate = null,
        [FromQuery] int limit = 10)
    {
        try
        {
            var request = new AnalyticsRequest
            {
                SellerId = sellerId,
                StartDate = startDate ?? DateTime.UtcNow.AddDays(-30),
                EndDate = endDate ?? DateTime.UtcNow,
                Limit = limit
            };

            var performance = await _analyticsService.GetProductPerformanceAsync(request);
            return Ok(performance);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting product performance");
            return StatusCode(500, new { error = "Internal server error" });
        }
    }

    /// <summary>
    /// Get customer analytics
    /// </summary>
    [HttpGet("customers")]
    public async Task<ActionResult<CustomerAnalyticsDto>> GetCustomerAnalytics(
        [FromQuery] int? sellerId = null,
        [FromQuery] DateTime? startDate = null,
        [FromQuery] DateTime? endDate = null)
    {
        try
        {
            var request = new AnalyticsRequest
            {
                SellerId = sellerId,
                StartDate = startDate ?? DateTime.UtcNow.AddDays(-30),
                EndDate = endDate ?? DateTime.UtcNow
            };

            var analytics = await _analyticsService.GetCustomerAnalyticsAsync(request);
            return Ok(analytics);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting customer analytics");
            return StatusCode(500, new { error = "Internal server error" });
        }
    }

    /// <summary>
    /// Get financial analytics
    /// </summary>
    [HttpGet("financial")]
    public async Task<ActionResult<FinancialAnalyticsDto>> GetFinancialAnalytics(
        [FromQuery] int? sellerId = null,
        [FromQuery] DateTime? startDate = null,
        [FromQuery] DateTime? endDate = null)
    {
        try
        {
            var request = new AnalyticsRequest
            {
                SellerId = sellerId,
                StartDate = startDate ?? DateTime.UtcNow.AddDays(-30),
                EndDate = endDate ?? DateTime.UtcNow
            };

            var analytics = await _analyticsService.GetFinancialAnalyticsAsync(request);
            return Ok(analytics);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting financial analytics");
            return StatusCode(500, new { error = "Internal server error" });
        }
    }

    /// <summary>
    /// Get inventory analytics
    /// </summary>
    [HttpGet("inventory")]
    public async Task<ActionResult<InventoryAnalyticsDto>> GetInventoryAnalytics(
        [FromQuery] int? sellerId = null)
    {
        try
        {
            var request = new AnalyticsRequest
            {
                SellerId = sellerId
            };

            var analytics = await _analyticsService.GetInventoryAnalyticsAsync(request);
            return Ok(analytics);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting inventory analytics");
            return StatusCode(500, new { error = "Internal server error" });
        }
    }

    /// <summary>
    /// Export analytics data
    /// </summary>
    [HttpPost("export")]
    public async Task<ActionResult> ExportAnalytics([FromBody] ExportRequest request)
    {
        try
        {
            var exportData = await _analyticsService.ExportAnalyticsAsync(request);
            
            var contentType = request.Format.ToLower() switch
            {
                "csv" => "text/csv",
                "excel" => "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                "pdf" => "application/pdf",
                _ => "application/octet-stream"
            };

            var fileName = $"analytics_{DateTime.UtcNow:yyyyMMdd_HHmmss}.{request.Format.ToLower()}";
            
            return File(exportData, contentType, fileName);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error exporting analytics");
            return StatusCode(500, new { error = "Internal server error" });
        }
    }

    /// <summary>
    /// Get real-time metrics
    /// </summary>
    [HttpGet("realtime")]
    public async Task<ActionResult<RealTimeMetricsDto>> GetRealTimeMetrics(
        [FromQuery] int? sellerId = null)
    {
        try
        {
            var metrics = await _analyticsService.GetRealTimeMetricsAsync(sellerId);
            return Ok(metrics);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting real-time metrics");
            return StatusCode(500, new { error = "Internal server error" });
        }
    }

    /// <summary>
    /// Get comparison analytics between periods
    /// </summary>
    [HttpGet("comparison")]
    public async Task<ActionResult<ComparisonAnalyticsDto>> GetComparisonAnalytics(
        [FromQuery] int? sellerId = null,
        [FromQuery] DateTime? currentStartDate = null,
        [FromQuery] DateTime? currentEndDate = null,
        [FromQuery] DateTime? previousStartDate = null,
        [FromQuery] DateTime? previousEndDate = null)
    {
        try
        {
            var request = new ComparisonRequest
            {
                SellerId = sellerId,
                CurrentPeriod = new AnalyticsRequest
                {
                    StartDate = currentStartDate ?? DateTime.UtcNow.AddDays(-30),
                    EndDate = currentEndDate ?? DateTime.UtcNow
                },
                PreviousPeriod = new AnalyticsRequest
                {
                    StartDate = previousStartDate ?? DateTime.UtcNow.AddDays(-60),
                    EndDate = previousEndDate ?? DateTime.UtcNow.AddDays(-30)
                }
            };

            var comparison = await _analyticsService.GetComparisonAnalyticsAsync(request);
            return Ok(comparison);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting comparison analytics");
            return StatusCode(500, new { error = "Internal server error" });
        }
    }
}
