// Cart Persistence JavaScript Module
window.CartPersistence = (function() {
    'use strict';

    const STORAGE_KEYS = {
        GUEST_USER_ID: 'guestUserId',
        GUEST_CART_TIMESTAMP: 'guestCartTimestamp',
        CART_BACKUP_PREFIX: 'cart_backup_',
        CART_SYNC_SETTINGS: 'cartSyncSettings',
        LAST_SYNC_TIMESTAMP: 'lastSyncTimestamp'
    };

    const DEFAULT_SETTINGS = {
        autoSync: true,
        conflictStrategy: 'MergeAddQuantities',
        backupOnLogout: true,
        maxBackups: 5,
        backupRetentionDays: 7
    };

    // Utilitaires de stockage local
    const Storage = {
        get: function(key) {
            try {
                const value = localStorage.getItem(key);
                return value ? JSON.parse(value) : null;
            } catch (e) {
                console.warn('Error reading from localStorage:', e);
                return null;
            }
        },

        set: function(key, value) {
            try {
                localStorage.setItem(key, JSON.stringify(value));
                return true;
            } catch (e) {
                console.warn('Error writing to localStorage:', e);
                return false;
            }
        },

        remove: function(key) {
            try {
                localStorage.removeItem(key);
                return true;
            } catch (e) {
                console.warn('Error removing from localStorage:', e);
                return false;
            }
        },

        clear: function() {
            try {
                localStorage.clear();
                return true;
            } catch (e) {
                console.warn('Error clearing localStorage:', e);
                return false;
            }
        }
    };

    // Gestion des IDs invités
    const GuestManager = {
        generateGuestId: function() {
            return 'guest_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
        },

        getGuestId: function() {
            let guestId = Storage.get(STORAGE_KEYS.GUEST_USER_ID);
            if (!guestId) {
                guestId = this.generateGuestId();
                this.setGuestId(guestId);
            }
            return guestId;
        },

        setGuestId: function(guestId) {
            Storage.set(STORAGE_KEYS.GUEST_USER_ID, guestId);
            Storage.set(STORAGE_KEYS.GUEST_CART_TIMESTAMP, Date.now());
        },

        clearGuestData: function() {
            Storage.remove(STORAGE_KEYS.GUEST_USER_ID);
            Storage.remove(STORAGE_KEYS.GUEST_CART_TIMESTAMP);
        },

        isGuestIdExpired: function(maxAgeHours = 24) {
            const timestamp = Storage.get(STORAGE_KEYS.GUEST_CART_TIMESTAMP);
            if (!timestamp) return true;
            
            const ageHours = (Date.now() - timestamp) / (1000 * 60 * 60);
            return ageHours > maxAgeHours;
        }
    };

    // Gestion des backups
    const BackupManager = {
        createBackup: function(cartData, userId) {
            const backup = {
                userId: userId,
                cartData: cartData,
                timestamp: Date.now(),
                expiresAt: Date.now() + (DEFAULT_SETTINGS.backupRetentionDays * 24 * 60 * 60 * 1000)
            };

            const backupKey = STORAGE_KEYS.CART_BACKUP_PREFIX + Date.now();
            Storage.set(backupKey, backup);
            
            this.cleanupOldBackups();
            return backupKey;
        },

        getBackups: function(userId = null) {
            const backups = [];
            const keys = Object.keys(localStorage);
            
            keys.forEach(key => {
                if (key.startsWith(STORAGE_KEYS.CART_BACKUP_PREFIX)) {
                    const backup = Storage.get(key);
                    if (backup && (!userId || backup.userId === userId)) {
                        if (backup.expiresAt > Date.now()) {
                            backups.push({ key, ...backup });
                        } else {
                            Storage.remove(key); // Nettoyer les backups expirés
                        }
                    }
                }
            });

            return backups.sort((a, b) => b.timestamp - a.timestamp);
        },

        restoreBackup: function(backupKey) {
            const backup = Storage.get(backupKey);
            if (backup && backup.expiresAt > Date.now()) {
                return backup.cartData;
            }
            return null;
        },

        cleanupOldBackups: function() {
            const backups = this.getBackups();
            if (backups.length > DEFAULT_SETTINGS.maxBackups) {
                const toRemove = backups.slice(DEFAULT_SETTINGS.maxBackups);
                toRemove.forEach(backup => Storage.remove(backup.key));
            }
        }
    };

    // Gestion de la synchronisation
    const SyncManager = {
        getSettings: function() {
            return Storage.get(STORAGE_KEYS.CART_SYNC_SETTINGS) || DEFAULT_SETTINGS;
        },

        updateSettings: function(settings) {
            const currentSettings = this.getSettings();
            const newSettings = { ...currentSettings, ...settings };
            Storage.set(STORAGE_KEYS.CART_SYNC_SETTINGS, newSettings);
        },

        markSyncCompleted: function() {
            Storage.set(STORAGE_KEYS.LAST_SYNC_TIMESTAMP, Date.now());
        },

        getLastSyncTime: function() {
            return Storage.get(STORAGE_KEYS.LAST_SYNC_TIMESTAMP);
        },

        shouldAutoSync: function() {
            const settings = this.getSettings();
            return settings.autoSync;
        }
    };

    // Détection des changements d'état d'authentification
    const AuthStateManager = {
        currentUserId: null,
        isAuthenticated: false,
        callbacks: [],

        init: function() {
            // Écouter les changements d'état d'authentification
            window.addEventListener('storage', (e) => {
                if (e.key === 'authToken' || e.key === 'userId') {
                    this.checkAuthState();
                }
            });

            // Vérification périodique
            setInterval(() => this.checkAuthState(), 5000);
            
            this.checkAuthState();
        },

        checkAuthState: function() {
            const token = localStorage.getItem('authToken');
            const userId = localStorage.getItem('userId');
            const wasAuthenticated = this.isAuthenticated;
            const previousUserId = this.currentUserId;

            this.isAuthenticated = !!token;
            this.currentUserId = userId;

            // Détecter les changements d'état
            if (!wasAuthenticated && this.isAuthenticated) {
                this.onLogin(userId);
            } else if (wasAuthenticated && !this.isAuthenticated) {
                this.onLogout(previousUserId);
            } else if (this.isAuthenticated && previousUserId !== userId) {
                this.onUserChanged(previousUserId, userId);
            }
        },

        onLogin: function(userId) {
            console.log('User logged in:', userId);
            this.notifyCallbacks('login', { userId });
        },

        onLogout: function(previousUserId) {
            console.log('User logged out:', previousUserId);
            this.notifyCallbacks('logout', { previousUserId });
        },

        onUserChanged: function(previousUserId, newUserId) {
            console.log('User changed:', previousUserId, '->', newUserId);
            this.notifyCallbacks('userChanged', { previousUserId, newUserId });
        },

        addCallback: function(callback) {
            this.callbacks.push(callback);
        },

        notifyCallbacks: function(event, data) {
            this.callbacks.forEach(callback => {
                try {
                    callback(event, data);
                } catch (e) {
                    console.error('Error in auth state callback:', e);
                }
            });
        }
    };

    // API publique
    return {
        // Initialisation
        init: function() {
            AuthStateManager.init();
            console.log('Cart Persistence initialized');
        },

        // Gestion des invités
        getGuestId: function() {
            return GuestManager.getGuestId();
        },

        clearGuestData: function() {
            GuestManager.clearGuestData();
        },

        isGuestIdExpired: function(maxAgeHours) {
            return GuestManager.isGuestIdExpired(maxAgeHours);
        },

        // Gestion des backups
        createBackup: function(cartData, userId) {
            return BackupManager.createBackup(cartData, userId);
        },

        getBackups: function(userId) {
            return BackupManager.getBackups(userId);
        },

        restoreBackup: function(backupKey) {
            return BackupManager.restoreBackup(backupKey);
        },

        // Gestion de la synchronisation
        getSyncSettings: function() {
            return SyncManager.getSettings();
        },

        updateSyncSettings: function(settings) {
            SyncManager.updateSettings(settings);
        },

        markSyncCompleted: function() {
            SyncManager.markSyncCompleted();
        },

        shouldAutoSync: function() {
            return SyncManager.shouldAutoSync();
        },

        // Gestion de l'état d'authentification
        onAuthStateChange: function(callback) {
            AuthStateManager.addCallback(callback);
        },

        getCurrentUserId: function() {
            return AuthStateManager.currentUserId;
        },

        isAuthenticated: function() {
            return AuthStateManager.isAuthenticated;
        },

        // Utilitaires
        getLocalStorageKeys: function() {
            return Object.keys(localStorage);
        },

        getStorageUsage: function() {
            let total = 0;
            for (let key in localStorage) {
                if (localStorage.hasOwnProperty(key)) {
                    total += localStorage[key].length + key.length;
                }
            }
            return {
                used: total,
                usedKB: Math.round(total / 1024 * 100) / 100,
                available: 5120 - total, // Estimation de 5MB de limite
                availableKB: Math.round((5120 - total) / 1024 * 100) / 100
            };
        },

        // Nettoyage
        cleanup: function() {
            BackupManager.cleanupOldBackups();
            
            // Nettoyer les IDs invités expirés
            if (GuestManager.isGuestIdExpired()) {
                GuestManager.clearGuestData();
            }
        }
    };
})();

// Auto-initialisation
document.addEventListener('DOMContentLoaded', function() {
    window.CartPersistence.init();
});

// Nettoyage périodique
setInterval(function() {
    window.CartPersistence.cleanup();
}, 60000); // Toutes les minutes

// Fonctions globales pour l'interopérabilité avec Blazor
window.getCurrentUserId = function() {
    return window.CartPersistence.getCurrentUserId();
};

window.getLocalStorageKeys = function() {
    return window.CartPersistence.getLocalStorageKeys();
};

window.showToast = function(message, type = 'info', title = '') {
    // Fonction pour afficher des notifications toast
    console.log(`Toast [${type}]: ${title} - ${message}`);
    
    // Ici, vous pourriez intégrer avec une bibliothèque de toast comme Toastr
    if (window.toastr) {
        window.toastr[type](message, title);
    } else {
        alert(`${title}: ${message}`);
    }
};
