using System.Collections.Generic;

namespace NafaPlace.Payment.API.Models
{
    public class CreateCheckoutSessionRequest
    {
        public string Currency { get; set; } = "gnf";
        public string SuccessUrl { get; set; } = string.Empty;
        public string CancelUrl { get; set; } = string.Empty;
        public List<SessionLineItem> Items { get; set; } = new();
        public string? OrderId { get; set; }
        public string? CustomerEmail { get; set; }
        public Dictionary<string, string>? Metadata { get; set; }
    }

    public class SessionLineItem
    {
        public string Name { get; set; } = string.Empty;
        public long UnitAmount { get; set; }
        public int Quantity { get; set; }
        public string ImageUrl { get; set; } = string.Empty;
        public string? Description { get; set; }
    }

    public class CreateRefundRequest
    {
        public string PaymentIntentId { get; set; } = string.Empty;
        public long? Amount { get; set; } // If null, refunds the full amount
        public string? Reason { get; set; } = "requested_by_customer";
    }

    public class PaymentStatusResponse
    {
        public string Status { get; set; } = string.Empty;
        public string PaymentIntentId { get; set; } = string.Empty;
        public long Amount { get; set; }
        public string Currency { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; }
        public string? FailureReason { get; set; }
    }



    public class WebhookEventResponse
    {
        public string EventType { get; set; } = string.Empty;
        public string ObjectId { get; set; } = string.Empty;
        public bool Processed { get; set; }
        public DateTime ProcessedAt { get; set; }
    }
}