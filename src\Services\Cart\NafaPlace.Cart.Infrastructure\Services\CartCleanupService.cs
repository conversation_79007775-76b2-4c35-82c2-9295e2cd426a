using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using NafaPlace.Cart.Infrastructure.Services;

namespace NafaPlace.Cart.Infrastructure.Services;

public class CartCleanupService : BackgroundService
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<CartCleanupService> _logger;
    private readonly CartCleanupSettings _settings;

    public CartCleanupService(
        IServiceProvider serviceProvider,
        ILogger<CartCleanupService> logger,
        IOptions<CartCleanupSettings> settings)
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
        _settings = settings.Value;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("Cart cleanup service started");

        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                await PerformCleanupAsync();
                await Task.Delay(TimeSpan.FromHours(_settings.CleanupIntervalHours), stoppingToken);
            }
            catch (OperationCanceledException)
            {
                _logger.LogInformation("Cart cleanup service is stopping");
                break;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during cart cleanup");
                await Task.Delay(TimeSpan.FromMinutes(30), stoppingToken); // Retry after 30 minutes
            }
        }
    }

    private async Task PerformCleanupAsync()
    {
        using var scope = _serviceProvider.CreateScope();
        var persistenceService = scope.ServiceProvider.GetRequiredService<IEnhancedCartPersistenceService>();

        _logger.LogInformation("Starting cart cleanup process");

        try
        {
            // Nettoyer les paniers invités expirés
            var cleanedUp = await persistenceService.CleanupExpiredCartsAsync();
            
            if (cleanedUp)
            {
                // Obtenir les statistiques après nettoyage
                var stats = await persistenceService.GetPersistenceStatsAsync();
                
                _logger.LogInformation("Cart cleanup completed. Current stats: {TotalCarts} total, {GuestCarts} guest, {UserCarts} user", 
                    stats.TotalCarts, stats.GuestCarts, stats.UserCarts);
            }

            // Nettoyer les backups expirés si activé
            if (_settings.CleanupBackups)
            {
                await CleanupExpiredBackupsAsync();
            }

            // Optimiser le cache si activé
            if (_settings.OptimizeCache)
            {
                await OptimizeCacheAsync();
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during cart cleanup process");
        }
    }

    private async Task CleanupExpiredBackupsAsync()
    {
        try
        {
            // Cette méthode nettoierait les backups expirés
            // Pour l'instant, on log juste l'action
            _logger.LogDebug("Cleaning up expired cart backups");
            await Task.CompletedTask;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error cleaning up expired backups");
        }
    }

    private async Task OptimizeCacheAsync()
    {
        try
        {
            // Cette méthode optimiserait le cache Redis
            // Pour l'instant, on log juste l'action
            _logger.LogDebug("Optimizing cart cache");
            await Task.CompletedTask;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error optimizing cache");
        }
    }
}

public class CartCleanupSettings
{
    public int CleanupIntervalHours { get; set; } = 24; // Nettoyage quotidien
    public bool CleanupBackups { get; set; } = true;
    public bool OptimizeCache { get; set; } = true;
    public int MaxCleanupBatchSize { get; set; } = 1000;
}

// Service pour les métriques de nettoyage
public interface ICartCleanupMetricsService
{
    Task RecordCleanupMetricsAsync(CartCleanupMetrics metrics);
    Task<List<CartCleanupMetrics>> GetCleanupHistoryAsync(DateTime startDate, DateTime endDate);
    Task<CartCleanupSummary> GetCleanupSummaryAsync();
}

public class CartCleanupMetricsService : ICartCleanupMetricsService
{
    private readonly ILogger<CartCleanupMetricsService> _logger;
    private readonly List<CartCleanupMetrics> _metricsHistory = new();

    public CartCleanupMetricsService(ILogger<CartCleanupMetricsService> logger)
    {
        _logger = logger;
    }

    public async Task RecordCleanupMetricsAsync(CartCleanupMetrics metrics)
    {
        try
        {
            _metricsHistory.Add(metrics);
            
            // Garder seulement les 30 derniers jours
            var cutoffDate = DateTime.UtcNow.AddDays(-30);
            _metricsHistory.RemoveAll(m => m.CleanupDate < cutoffDate);

            _logger.LogInformation("Cleanup metrics recorded: {CartsRemoved} carts removed, {Duration}ms duration", 
                metrics.CartsRemoved, metrics.DurationMs);

            await Task.CompletedTask;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error recording cleanup metrics");
        }
    }

    public async Task<List<CartCleanupMetrics>> GetCleanupHistoryAsync(DateTime startDate, DateTime endDate)
    {
        await Task.CompletedTask;
        return _metricsHistory
            .Where(m => m.CleanupDate >= startDate && m.CleanupDate <= endDate)
            .OrderByDescending(m => m.CleanupDate)
            .ToList();
    }

    public async Task<CartCleanupSummary> GetCleanupSummaryAsync()
    {
        await Task.CompletedTask;
        
        var last30Days = _metricsHistory
            .Where(m => m.CleanupDate >= DateTime.UtcNow.AddDays(-30))
            .ToList();

        return new CartCleanupSummary
        {
            TotalCleanupRuns = last30Days.Count,
            TotalCartsRemoved = last30Days.Sum(m => m.CartsRemoved),
            AverageDurationMs = last30Days.Any() ? last30Days.Average(m => m.DurationMs) : 0,
            LastCleanupDate = last30Days.Any() ? last30Days.Max(m => m.CleanupDate) : null,
            AverageCartsPerCleanup = last30Days.Any() ? last30Days.Average(m => m.CartsRemoved) : 0
        };
    }
}

public class CartCleanupMetrics
{
    public DateTime CleanupDate { get; set; }
    public int CartsRemoved { get; set; }
    public int BackupsRemoved { get; set; }
    public long DurationMs { get; set; }
    public int ErrorCount { get; set; }
    public Dictionary<string, object> AdditionalData { get; set; } = new();
}

public class CartCleanupSummary
{
    public int TotalCleanupRuns { get; set; }
    public int TotalCartsRemoved { get; set; }
    public double AverageDurationMs { get; set; }
    public DateTime? LastCleanupDate { get; set; }
    public double AverageCartsPerCleanup { get; set; }
}

// Extensions pour l'enregistrement des services
public static class CartCleanupServiceExtensions
{
    public static IServiceCollection AddCartCleanupServices(this IServiceCollection services, Action<CartCleanupSettings>? configureSettings = null)
    {
        // Configuration
        if (configureSettings != null)
        {
            services.Configure(configureSettings);
        }
        else
        {
            services.Configure<CartCleanupSettings>(settings => { });
        }

        // Services
        services.AddSingleton<ICartCleanupMetricsService, CartCleanupMetricsService>();
        services.AddHostedService<CartCleanupService>();

        return services;
    }
}

// Service de monitoring pour les paniers
public interface ICartMonitoringService
{
    Task<CartHealthStatus> GetCartHealthStatusAsync();
    Task<List<CartAlert>> GetActiveAlertsAsync();
    Task<CartPerformanceMetrics> GetPerformanceMetricsAsync();
}

public class CartMonitoringService : ICartMonitoringService
{
    private readonly IEnhancedCartPersistenceService _persistenceService;
    private readonly ILogger<CartMonitoringService> _logger;

    public CartMonitoringService(
        IEnhancedCartPersistenceService persistenceService,
        ILogger<CartMonitoringService> logger)
    {
        _persistenceService = persistenceService;
        _logger = logger;
    }

    public async Task<CartHealthStatus> GetCartHealthStatusAsync()
    {
        try
        {
            var stats = await _persistenceService.GetPersistenceStatsAsync();
            
            var healthStatus = new CartHealthStatus
            {
                IsHealthy = true,
                TotalCarts = stats.TotalCarts,
                GuestCarts = stats.GuestCarts,
                UserCarts = stats.UserCarts,
                EmptyCartsPercentage = stats.TotalCarts > 0 ? (double)stats.EmptyCarts / stats.TotalCarts * 100 : 0,
                AverageItemsPerCart = stats.AverageItemsPerCart,
                LastChecked = DateTime.UtcNow
            };

            // Vérifier les seuils de santé
            if (healthStatus.EmptyCartsPercentage > 80)
            {
                healthStatus.IsHealthy = false;
                healthStatus.Issues.Add("Trop de paniers vides (>80%)");
            }

            if (stats.GuestCarts > stats.UserCarts * 5)
            {
                healthStatus.IsHealthy = false;
                healthStatus.Issues.Add("Trop de paniers invités par rapport aux paniers utilisateurs");
            }

            return healthStatus;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting cart health status");
            return new CartHealthStatus { IsHealthy = false, Issues = { "Erreur lors de la vérification" } };
        }
    }

    public async Task<List<CartAlert>> GetActiveAlertsAsync()
    {
        var alerts = new List<CartAlert>();
        
        try
        {
            var healthStatus = await GetCartHealthStatusAsync();
            
            if (!healthStatus.IsHealthy)
            {
                foreach (var issue in healthStatus.Issues)
                {
                    alerts.Add(new CartAlert
                    {
                        Type = CartAlertType.Health,
                        Severity = CartAlertSeverity.Warning,
                        Message = issue,
                        CreatedAt = DateTime.UtcNow
                    });
                }
            }

            // Autres vérifications d'alertes...
            
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting active alerts");
        }

        return alerts;
    }

    public async Task<CartPerformanceMetrics> GetPerformanceMetricsAsync()
    {
        try
        {
            // Simuler des métriques de performance
            return new CartPerformanceMetrics
            {
                AverageLoadTimeMs = 150,
                AverageSaveTimeMs = 75,
                CacheHitRate = 85.5,
                ErrorRate = 0.1,
                ThroughputPerSecond = 1250,
                LastMeasured = DateTime.UtcNow
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting performance metrics");
            return new CartPerformanceMetrics();
        }
    }
}

// Classes de support pour le monitoring
public class CartHealthStatus
{
    public bool IsHealthy { get; set; }
    public int TotalCarts { get; set; }
    public int GuestCarts { get; set; }
    public int UserCarts { get; set; }
    public double EmptyCartsPercentage { get; set; }
    public double AverageItemsPerCart { get; set; }
    public DateTime LastChecked { get; set; }
    public List<string> Issues { get; set; } = new();
}

public class CartAlert
{
    public CartAlertType Type { get; set; }
    public CartAlertSeverity Severity { get; set; }
    public string Message { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; }
    public bool IsResolved { get; set; }
}

public enum CartAlertType
{
    Health,
    Performance,
    Security,
    Capacity
}

public enum CartAlertSeverity
{
    Info,
    Warning,
    Error,
    Critical
}

public class CartPerformanceMetrics
{
    public double AverageLoadTimeMs { get; set; }
    public double AverageSaveTimeMs { get; set; }
    public double CacheHitRate { get; set; }
    public double ErrorRate { get; set; }
    public double ThroughputPerSecond { get; set; }
    public DateTime LastMeasured { get; set; }
}
