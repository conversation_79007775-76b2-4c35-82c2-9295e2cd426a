using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using NafaPlace.Catalog.Infrastructure.Data;
using NafaPlace.Catalog.Domain.Entities;

namespace NafaPlace.Catalog.Infrastructure.Services;

public class ImageManagementService : IImageManagementService
{
    private readonly CatalogDbContext _context;
    private readonly IEnhancedCloudinaryService _cloudinaryService;
    private readonly ILogger<ImageManagementService> _logger;

    public ImageManagementService(
        CatalogDbContext context,
        IEnhancedCloudinaryService cloudinaryService,
        ILogger<ImageManagementService> logger)
    {
        _context = context;
        _cloudinaryService = cloudinaryService;
        _logger = logger;
    }

    public async Task<BulkImageUploadResponse> UploadProductImagesAsync(BulkImageUploadRequest request)
    {
        var response = new BulkImageUploadResponse();
        
        try
        {
            // Vérifier que le produit existe
            var product = await _context.Products.FindAsync(request.ProductId);
            if (product == null)
            {
                response.Errors.Add($"Produit avec l'ID {request.ProductId} introuvable");
                return response;
            }

            var uploadTasks = request.Images.Select(async (imageItem, index) =>
            {
                try
                {
                    var fileName = $"product_{request.ProductId}_image_{index + 1}";
                    var options = new ImageUploadOptions
                    {
                        Tags = new List<string> { "product", $"product_{request.ProductId}" },
                        OptimizationOptions = new ImageOptimizationOptions
                        {
                            MaxWidth = 1920,
                            MaxHeight = 1080,
                            Quality = 85
                        }
                    };

                    var uploadResult = await _cloudinaryService.UploadImageAsync(
                        imageItem.Image, fileName, "products", options);

                    if (uploadResult.Success)
                    {
                        // Créer l'entité ProductImage
                        var productImage = new ProductImage
                        {
                            ProductId = request.ProductId,
                            ImageUrl = uploadResult.Url!,
                            ThumbnailUrl = await _cloudinaryService.GenerateOptimizedUrlAsync(
                                uploadResult.PublicId!, 
                                new ImageTransformation { Width = 300, Height = 300 }),
                            IsMain = imageItem.IsMain,
                            AltText = imageItem.AltText,
                            DisplayOrder = imageItem.DisplayOrder ?? index,
                            PublicId = uploadResult.PublicId,
                            CreatedAt = DateTime.UtcNow
                        };

                        _context.ProductImages.Add(productImage);
                        response.SuccessCount++;
                        
                        return uploadResult;
                    }
                    else
                    {
                        response.Errors.Add($"Erreur upload image {index + 1}: {uploadResult.ErrorMessage}");
                        response.ErrorCount++;
                        return uploadResult;
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Erreur lors de l'upload de l'image {Index} pour le produit {ProductId}", 
                        index + 1, request.ProductId);
                    response.Errors.Add($"Erreur image {index + 1}: {ex.Message}");
                    response.ErrorCount++;
                    return new ImageUploadResult { Success = false, ErrorMessage = ex.Message };
                }
            });

            response.Results.AddRange(await Task.WhenAll(uploadTasks));

            // Gérer l'image principale
            if (request.Images.Any(i => i.IsMain))
            {
                await EnsureSingleMainImageAsync(request.ProductId);
            }

            await _context.SaveChangesAsync();
            response.Success = response.ErrorCount == 0;

            _logger.LogInformation("Upload terminé pour le produit {ProductId}: {SuccessCount} succès, {ErrorCount} erreurs", 
                request.ProductId, response.SuccessCount, response.ErrorCount);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'upload en lot pour le produit {ProductId}", request.ProductId);
            response.Errors.Add($"Erreur générale: {ex.Message}");
            response.Success = false;
        }

        return response;
    }

    public async Task<ProductImageManagementResponse> ManageProductImagesAsync(ProductImageManagementRequest request)
    {
        var response = new ProductImageManagementResponse();
        
        try
        {
            foreach (var action in request.Actions)
            {
                switch (action.Action.ToLower())
                {
                    case "add":
                        await HandleAddImageAction(request.ProductId, action, response);
                        break;
                    case "update":
                        await HandleUpdateImageAction(action, response);
                        break;
                    case "delete":
                        await HandleDeleteImageAction(action, response);
                        break;
                    case "reorder":
                        await HandleReorderImageAction(request.ProductId, action, response);
                        break;
                    default:
                        response.Errors.Add($"Action non reconnue: {action.Action}");
                        break;
                }
            }

            await _context.SaveChangesAsync();
            response.Images = await GetProductImageDtosAsync(request.ProductId);
            response.Success = response.Errors.Count == 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la gestion des images du produit {ProductId}", request.ProductId);
            response.Errors.Add($"Erreur générale: {ex.Message}");
            response.Success = false;
        }

        return response;
    }

    public async Task<List<ProductImageDto>> GetProductImagesAsync(int productId)
    {
        return await GetProductImageDtosAsync(productId);
    }

    public async Task<bool> DeleteProductImageAsync(int imageId)
    {
        try
        {
            var image = await _context.ProductImages.FindAsync(imageId);
            if (image == null) return false;

            // Supprimer de Cloudinary
            if (!string.IsNullOrEmpty(image.PublicId))
            {
                await _cloudinaryService.DeleteImageAsync(image.PublicId);
            }

            _context.ProductImages.Remove(image);
            await _context.SaveChangesAsync();

            _logger.LogInformation("Image {ImageId} supprimée avec succès", imageId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la suppression de l'image {ImageId}", imageId);
            return false;
        }
    }

    public async Task<bool> SetMainImageAsync(int productId, int imageId)
    {
        try
        {
            // Désactiver toutes les images principales du produit
            var existingMainImages = await _context.ProductImages
                .Where(pi => pi.ProductId == productId && pi.IsMain)
                .ToListAsync();

            foreach (var img in existingMainImages)
            {
                img.IsMain = false;
            }

            // Activer la nouvelle image principale
            var newMainImage = await _context.ProductImages.FindAsync(imageId);
            if (newMainImage != null && newMainImage.ProductId == productId)
            {
                newMainImage.IsMain = true;
                await _context.SaveChangesAsync();
                return true;
            }

            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la définition de l'image principale {ImageId} pour le produit {ProductId}", 
                imageId, productId);
            return false;
        }
    }

    public async Task<bool> ReorderImagesAsync(int productId, List<int> imageIds)
    {
        try
        {
            var images = await _context.ProductImages
                .Where(pi => pi.ProductId == productId && imageIds.Contains(pi.Id))
                .ToListAsync();

            for (int i = 0; i < imageIds.Count; i++)
            {
                var image = images.FirstOrDefault(img => img.Id == imageIds[i]);
                if (image != null)
                {
                    image.DisplayOrder = i;
                }
            }

            await _context.SaveChangesAsync();
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la réorganisation des images du produit {ProductId}", productId);
            return false;
        }
    }

    public async Task<ImageStatistics> GetImageStatisticsAsync()
    {
        try
        {
            var images = await _context.ProductImages.ToListAsync();
            
            return new ImageStatistics
            {
                TotalImages = images.Count,
                LastUpload = images.Any() ? images.Max(i => i.CreatedAt) : DateTime.MinValue,
                RecentUploads = images
                    .Where(i => i.CreatedAt >= DateTime.UtcNow.AddDays(-7))
                    .OrderByDescending(i => i.CreatedAt)
                    .Take(10)
                    .Select(i => i.ImageUrl)
                    .ToList()
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des statistiques d'images");
            return new ImageStatistics();
        }
    }

    public async Task<List<string>> GenerateImageVariantsAsync(string publicId, List<ImageVariant> variants)
    {
        var urls = new List<string>();
        
        foreach (var variant in variants)
        {
            var transformation = new ImageTransformation
            {
                Width = variant.Width,
                Height = variant.Height,
                Crop = variant.Crop,
                Quality = variant.Quality,
                Format = variant.Format
            };

            var url = await _cloudinaryService.GenerateOptimizedUrlAsync(publicId, transformation);
            urls.Add(url);
        }

        return urls;
    }

    // Méthodes privées
    private async Task HandleAddImageAction(int productId, ProductImageAction action, ProductImageManagementResponse response)
    {
        if (string.IsNullOrEmpty(action.Image))
        {
            response.Errors.Add("Image base64 manquante pour l'action 'add'");
            return;
        }

        var fileName = $"product_{productId}_image_{DateTime.UtcNow:yyyyMMdd_HHmmss}";
        var uploadResult = await _cloudinaryService.UploadImageAsync(action.Image, fileName, "products");

        if (uploadResult.Success)
        {
            var productImage = new ProductImage
            {
                ProductId = productId,
                ImageUrl = uploadResult.Url!,
                ThumbnailUrl = await _cloudinaryService.GenerateOptimizedUrlAsync(
                    uploadResult.PublicId!, 
                    new ImageTransformation { Width = 300, Height = 300 }),
                IsMain = action.IsMain ?? false,
                AltText = action.AltText,
                DisplayOrder = action.DisplayOrder ?? 0,
                PublicId = uploadResult.PublicId,
                CreatedAt = DateTime.UtcNow
            };

            _context.ProductImages.Add(productImage);
        }
        else
        {
            response.Errors.Add($"Erreur upload: {uploadResult.ErrorMessage}");
        }
    }

    private async Task HandleUpdateImageAction(ProductImageAction action, ProductImageManagementResponse response)
    {
        if (!action.ImageId.HasValue)
        {
            response.Errors.Add("ImageId manquant pour l'action 'update'");
            return;
        }

        var image = await _context.ProductImages.FindAsync(action.ImageId.Value);
        if (image == null)
        {
            response.Errors.Add($"Image {action.ImageId} introuvable");
            return;
        }

        if (action.IsMain.HasValue) image.IsMain = action.IsMain.Value;
        if (!string.IsNullOrEmpty(action.AltText)) image.AltText = action.AltText;
        if (action.DisplayOrder.HasValue) image.DisplayOrder = action.DisplayOrder.Value;
    }

    private async Task HandleDeleteImageAction(ProductImageAction action, ProductImageManagementResponse response)
    {
        if (!action.ImageId.HasValue)
        {
            response.Errors.Add("ImageId manquant pour l'action 'delete'");
            return;
        }

        var success = await DeleteProductImageAsync(action.ImageId.Value);
        if (!success)
        {
            response.Errors.Add($"Erreur lors de la suppression de l'image {action.ImageId}");
        }
    }

    private async Task HandleReorderImageAction(int productId, ProductImageAction action, ProductImageManagementResponse response)
    {
        // Cette action nécessite une logique spéciale, généralement gérée par ReorderImagesAsync
        response.Errors.Add("Action 'reorder' doit être gérée via ReorderImagesAsync");
        await Task.CompletedTask;
    }

    private async Task<List<ProductImageDto>> GetProductImageDtosAsync(int productId)
    {
        return await _context.ProductImages
            .Where(pi => pi.ProductId == productId)
            .OrderBy(pi => pi.DisplayOrder)
            .Select(pi => new ProductImageDto
            {
                Id = pi.Id,
                ProductId = pi.ProductId,
                ImageUrl = pi.ImageUrl,
                ThumbnailUrl = pi.ThumbnailUrl,
                IsMain = pi.IsMain,
                AltText = pi.AltText,
                DisplayOrder = pi.DisplayOrder,
                PublicId = pi.PublicId,
                CreatedAt = pi.CreatedAt
            })
            .ToListAsync();
    }

    private async Task EnsureSingleMainImageAsync(int productId)
    {
        var mainImages = await _context.ProductImages
            .Where(pi => pi.ProductId == productId && pi.IsMain)
            .OrderBy(pi => pi.DisplayOrder)
            .ToListAsync();

        if (mainImages.Count > 1)
        {
            // Garder seulement la première comme principale
            for (int i = 1; i < mainImages.Count; i++)
            {
                mainImages[i].IsMain = false;
            }
        }
    }
}
