namespace NafaPlace.Analytics.Infrastructure.DTOs;

// DTOs pour les données internes récupérées des autres services
public class OrderDataDto
{
    public int Id { get; set; }
    public string CustomerId { get; set; } = string.Empty;
    public int? SellerId { get; set; }
    public decimal TotalAmount { get; set; }
    public string Status { get; set; } = string.Empty;
    public string PaymentMethod { get; set; } = string.Empty;
    public string PaymentStatus { get; set; } = string.Empty;
    public DateTime OrderDate { get; set; }
    public DateTime? ShippedDate { get; set; }
    public DateTime? DeliveredDate { get; set; }
    public string? Region { get; set; }
    public string? City { get; set; }
    public List<OrderItemDataDto> Items { get; set; } = new();
    public Dictionary<string, object>? Metadata { get; set; }
}

public class OrderItemDataDto
{
    public int ProductId { get; set; }
    public string ProductName { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
    public int Quantity { get; set; }
    public decimal UnitPrice { get; set; }
    public decimal TotalPrice { get; set; }
}

public class ProductDataDto
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
    public int? SellerId { get; set; }
    public decimal Price { get; set; }
    public int Stock { get; set; }
    public int MinimumStock { get; set; }
    public int MaximumStock { get; set; }
    public string Status { get; set; } = string.Empty;
    public decimal Rating { get; set; }
    public int ReviewCount { get; set; }
    public int ViewCount { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? LastSoldAt { get; set; }
    public string ImageUrl { get; set; } = string.Empty;
}

public class CustomerDataDto
{
    public string Id { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; }
    public DateTime? LastOrderDate { get; set; }
    public int TotalOrders { get; set; }
    public decimal TotalSpent { get; set; }
    public string? Region { get; set; }
    public string? City { get; set; }
    public string Status { get; set; } = string.Empty;
    public bool IsVerified { get; set; }
}

public class UserDataDto
{
    public string Id { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string Role { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; }
    public DateTime? LastLoginAt { get; set; }
    public bool IsActive { get; set; }
    public bool IsVerified { get; set; }
    public string? Region { get; set; }
}

public class SellerDataDto
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string CompanyName { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; }
    public string Status { get; set; } = string.Empty;
    public decimal Rating { get; set; }
    public int ReviewCount { get; set; }
    public int ProductCount { get; set; }
    public decimal TotalRevenue { get; set; }
    public int TotalOrders { get; set; }
    public string? Region { get; set; }
}

public class ExpenseDataDto
{
    public int Id { get; set; }
    public string Category { get; set; } = string.Empty;
    public decimal Amount { get; set; }
    public DateTime Date { get; set; }
    public string Description { get; set; } = string.Empty;
    public int? SellerId { get; set; }
    public string Type { get; set; } = string.Empty; // fixed, variable, one-time
}

public class TaxDataDto
{
    public int Id { get; set; }
    public string TaxType { get; set; } = string.Empty;
    public decimal Amount { get; set; }
    public decimal Rate { get; set; }
    public DateTime Date { get; set; }
    public int? SellerId { get; set; }
    public string? OrderId { get; set; }
}

public class StockMovementDto
{
    public int Id { get; set; }
    public int ProductId { get; set; }
    public string ProductName { get; set; } = string.Empty;
    public string MovementType { get; set; } = string.Empty; // in, out, adjustment
    public int Quantity { get; set; }
    public int PreviousStock { get; set; }
    public int NewStock { get; set; }
    public DateTime Date { get; set; }
    public string? Reason { get; set; }
    public string? Reference { get; set; }
}

// DTOs pour les métriques calculées
public class CalculatedMetricsDto
{
    public decimal Revenue { get; set; }
    public int Orders { get; set; }
    public int Customers { get; set; }
    public decimal AverageOrderValue { get; set; }
    public decimal ConversionRate { get; set; }
    public DateTime CalculatedAt { get; set; }
    public string Period { get; set; } = string.Empty;
}

public class TrendDataDto
{
    public DateTime Date { get; set; }
    public decimal Value { get; set; }
    public decimal Change { get; set; }
    public string Metric { get; set; } = string.Empty;
    public string Trend { get; set; } = string.Empty; // up, down, stable
}

public class SegmentDataDto
{
    public string SegmentName { get; set; } = string.Empty;
    public string SegmentType { get; set; } = string.Empty; // customer, product, region
    public int Count { get; set; }
    public decimal Value { get; set; }
    public decimal Percentage { get; set; }
    public Dictionary<string, object>? Properties { get; set; }
}

public class AlertDataDto
{
    public int Id { get; set; }
    public string Type { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
    public string Severity { get; set; } = string.Empty; // low, medium, high, critical
    public DateTime CreatedAt { get; set; }
    public bool IsRead { get; set; }
    public int? ProductId { get; set; }
    public int? SellerId { get; set; }
    public Dictionary<string, object>? Data { get; set; }
}

public class ActivityDataDto
{
    public int Id { get; set; }
    public string Type { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string UserId { get; set; } = string.Empty;
    public string? UserName { get; set; }
    public DateTime Timestamp { get; set; }
    public string? EntityType { get; set; }
    public string? EntityId { get; set; }
    public Dictionary<string, object>? Data { get; set; }
}

// DTOs pour les configurations et paramètres
public class AnalyticsConfigDto
{
    public string Key { get; set; } = string.Empty;
    public string Value { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty;
    public string? Description { get; set; }
    public DateTime UpdatedAt { get; set; }
}

public class ReportConfigDto
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty;
    public string Schedule { get; set; } = string.Empty; // daily, weekly, monthly
    public List<string> Recipients { get; set; } = new();
    public Dictionary<string, object> Parameters { get; set; } = new();
    public bool IsActive { get; set; }
    public DateTime? LastRun { get; set; }
    public DateTime? NextRun { get; set; }
}

// DTOs pour les exports
public class ExportDataDto
{
    public string FileName { get; set; } = string.Empty;
    public string Format { get; set; } = string.Empty;
    public byte[] Data { get; set; } = Array.Empty<byte>();
    public string ContentType { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; }
    public long SizeBytes { get; set; }
}

public class ChartConfigDto
{
    public string ChartType { get; set; } = string.Empty; // line, bar, pie, area
    public string Title { get; set; } = string.Empty;
    public string XAxisLabel { get; set; } = string.Empty;
    public string YAxisLabel { get; set; } = string.Empty;
    public List<string> Colors { get; set; } = new();
    public Dictionary<string, object> Options { get; set; } = new();
}

// DTOs pour les benchmarks et comparaisons
public class BenchmarkDataDto
{
    public string Metric { get; set; } = string.Empty;
    public decimal Value { get; set; }
    public decimal IndustryAverage { get; set; }
    public decimal Percentile { get; set; }
    public string Performance { get; set; } = string.Empty; // excellent, good, average, poor
    public string? Recommendation { get; set; }
}

public class CompetitorDataDto
{
    public string Name { get; set; } = string.Empty;
    public decimal MarketShare { get; set; }
    public decimal Revenue { get; set; }
    public int ProductCount { get; set; }
    public decimal AverageRating { get; set; }
    public string Strengths { get; set; } = string.Empty;
    public string Weaknesses { get; set; } = string.Empty;
}

// DTOs pour les prédictions et forecasting
public class ForecastDataDto
{
    public DateTime Date { get; set; }
    public decimal PredictedValue { get; set; }
    public decimal ConfidenceInterval { get; set; }
    public string Metric { get; set; } = string.Empty;
    public string Model { get; set; } = string.Empty;
    public decimal Accuracy { get; set; }
}

public class SeasonalityDataDto
{
    public string Period { get; set; } = string.Empty; // month, quarter, season
    public decimal Factor { get; set; }
    public decimal AverageValue { get; set; }
    public string Trend { get; set; } = string.Empty;
    public List<string> Insights { get; set; } = new();
}

// DTOs pour les KPIs et objectifs
public class KpiDataDto
{
    public string Name { get; set; } = string.Empty;
    public decimal CurrentValue { get; set; }
    public decimal TargetValue { get; set; }
    public decimal Achievement { get; set; }
    public string Status { get; set; } = string.Empty; // on-track, at-risk, behind
    public DateTime LastUpdated { get; set; }
    public string Unit { get; set; } = string.Empty;
    public string? Description { get; set; }
}

public class GoalDataDto
{
    public int Id { get; set; }
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public decimal TargetValue { get; set; }
    public decimal CurrentValue { get; set; }
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
    public string Status { get; set; } = string.Empty;
    public int? SellerId { get; set; }
    public List<KpiDataDto> Kpis { get; set; } = new();
}
