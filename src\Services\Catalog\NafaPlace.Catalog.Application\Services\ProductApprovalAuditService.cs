using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using NafaPlace.Catalog.Infrastructure.Data;
using NafaPlace.Catalog.Domain.Models;
using NafaPlace.Catalog.Application.DTOs.Product;
using System.Text.Json;

namespace NafaPlace.Catalog.Application.Services;

public interface IProductApprovalAuditService
{
    Task LogApprovalActionAsync(int productId, ProductApprovalAction action, string performedBy, string? notes = null, string? reason = null);
    Task<List<ProductApprovalAuditDto>> GetProductAuditHistoryAsync(int productId);
    Task<List<ProductApprovalAuditDto>> GetUserAuditHistoryAsync(string userId, DateTime? startDate = null, DateTime? endDate = null);
    Task<ProductApprovalAuditStatsDto> GetAuditStatsAsync(DateTime? startDate = null, DateTime? endDate = null);
    Task<List<ProductApprovalAuditDto>> SearchAuditLogsAsync(ProductApprovalAuditSearchDto searchCriteria);
    Task<bool> ExportAuditLogsAsync(ProductApprovalAuditExportDto exportRequest);
}

public class ProductApprovalAuditService : IProductApprovalAuditService
{
    private readonly CatalogDbContext _context;
    private readonly ILogger<ProductApprovalAuditService> _logger;

    public ProductApprovalAuditService(CatalogDbContext context, ILogger<ProductApprovalAuditService> logger)
    {
        _context = context;
        _logger = logger;
    }

    public async Task LogApprovalActionAsync(int productId, ProductApprovalAction action, string performedBy, string? notes = null, string? reason = null)
    {
        try
        {
            var product = await _context.Products
                .Include(p => p.Seller)
                .FirstOrDefaultAsync(p => p.Id == productId);

            if (product == null)
            {
                _logger.LogWarning("Attempted to log approval action for non-existent product {ProductId}", productId);
                return;
            }

            var auditLog = new ProductApprovalAudit
            {
                ProductId = productId,
                ProductName = product.Name,
                SellerId = product.SellerId,
                SellerName = product.Seller?.Name ?? "Vendeur inconnu",
                Action = action,
                PerformedBy = performedBy,
                Notes = notes,
                Reason = reason,
                Timestamp = DateTime.UtcNow,
                IpAddress = GetCurrentIpAddress(),
                UserAgent = GetCurrentUserAgent(),
                AdditionalData = CreateAdditionalData(product, action)
            };

            _context.ProductApprovalAudits.Add(auditLog);
            await _context.SaveChangesAsync();

            _logger.LogInformation("Approval action logged: ProductId={ProductId}, Action={Action}, PerformedBy={PerformedBy}", 
                productId, action, performedBy);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error logging approval action for ProductId={ProductId}, Action={Action}", productId, action);
        }
    }

    public async Task<List<ProductApprovalAuditDto>> GetProductAuditHistoryAsync(int productId)
    {
        try
        {
            var auditLogs = await _context.ProductApprovalAudits
                .Where(a => a.ProductId == productId)
                .OrderByDescending(a => a.Timestamp)
                .ToListAsync();

            return auditLogs.Select(MapToDto).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting audit history for ProductId={ProductId}", productId);
            return new List<ProductApprovalAuditDto>();
        }
    }

    public async Task<List<ProductApprovalAuditDto>> GetUserAuditHistoryAsync(string userId, DateTime? startDate = null, DateTime? endDate = null)
    {
        try
        {
            var query = _context.ProductApprovalAudits
                .Where(a => a.PerformedBy == userId);

            if (startDate.HasValue)
                query = query.Where(a => a.Timestamp >= startDate.Value);

            if (endDate.HasValue)
                query = query.Where(a => a.Timestamp <= endDate.Value);

            var auditLogs = await query
                .OrderByDescending(a => a.Timestamp)
                .Take(1000) // Limiter à 1000 entrées
                .ToListAsync();

            return auditLogs.Select(MapToDto).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting user audit history for UserId={UserId}", userId);
            return new List<ProductApprovalAuditDto>();
        }
    }

    public async Task<ProductApprovalAuditStatsDto> GetAuditStatsAsync(DateTime? startDate = null, DateTime? endDate = null)
    {
        try
        {
            startDate ??= DateTime.UtcNow.AddDays(-30);
            endDate ??= DateTime.UtcNow;

            var auditLogs = await _context.ProductApprovalAudits
                .Where(a => a.Timestamp >= startDate && a.Timestamp <= endDate)
                .ToListAsync();

            var stats = new ProductApprovalAuditStatsDto
            {
                TotalActions = auditLogs.Count,
                ActionsByType = auditLogs
                    .GroupBy(a => a.Action)
                    .ToDictionary(g => g.Key.ToString(), g => g.Count()),
                ActionsByUser = auditLogs
                    .GroupBy(a => a.PerformedBy)
                    .ToDictionary(g => g.Key, g => g.Count()),
                ActionsByDay = auditLogs
                    .GroupBy(a => a.Timestamp.Date)
                    .ToDictionary(g => g.Key, g => g.Count()),
                MostActiveUsers = auditLogs
                    .GroupBy(a => a.PerformedBy)
                    .OrderByDescending(g => g.Count())
                    .Take(10)
                    .ToDictionary(g => g.Key, g => g.Count()),
                RecentActions = auditLogs
                    .OrderByDescending(a => a.Timestamp)
                    .Take(20)
                    .Select(MapToDto)
                    .ToList()
            };

            return stats;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting audit stats");
            return new ProductApprovalAuditStatsDto();
        }
    }

    public async Task<List<ProductApprovalAuditDto>> SearchAuditLogsAsync(ProductApprovalAuditSearchDto searchCriteria)
    {
        try
        {
            var query = _context.ProductApprovalAudits.AsQueryable();

            // Appliquer les filtres
            if (searchCriteria.ProductId.HasValue)
                query = query.Where(a => a.ProductId == searchCriteria.ProductId.Value);

            if (!string.IsNullOrEmpty(searchCriteria.PerformedBy))
                query = query.Where(a => a.PerformedBy.Contains(searchCriteria.PerformedBy));

            if (searchCriteria.Action.HasValue)
                query = query.Where(a => a.Action == searchCriteria.Action.Value);

            if (searchCriteria.StartDate.HasValue)
                query = query.Where(a => a.Timestamp >= searchCriteria.StartDate.Value);

            if (searchCriteria.EndDate.HasValue)
                query = query.Where(a => a.Timestamp <= searchCriteria.EndDate.Value);

            if (!string.IsNullOrEmpty(searchCriteria.SearchTerm))
            {
                query = query.Where(a => 
                    a.ProductName.Contains(searchCriteria.SearchTerm) ||
                    a.SellerName.Contains(searchCriteria.SearchTerm) ||
                    a.Notes.Contains(searchCriteria.SearchTerm) ||
                    a.Reason.Contains(searchCriteria.SearchTerm));
            }

            // Appliquer le tri
            query = searchCriteria.SortDirection?.ToLower() == "asc" 
                ? query.OrderBy(a => a.Timestamp)
                : query.OrderByDescending(a => a.Timestamp);

            // Appliquer la pagination
            var auditLogs = await query
                .Skip((searchCriteria.Page - 1) * searchCriteria.PageSize)
                .Take(searchCriteria.PageSize)
                .ToListAsync();

            return auditLogs.Select(MapToDto).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching audit logs");
            return new List<ProductApprovalAuditDto>();
        }
    }

    public async Task<bool> ExportAuditLogsAsync(ProductApprovalAuditExportDto exportRequest)
    {
        try
        {
            // Cette méthode exporterait les logs d'audit vers un fichier
            // Pour l'instant, on simule juste l'export
            _logger.LogInformation("Audit logs export requested: Format={Format}, StartDate={StartDate}, EndDate={EndDate}", 
                exportRequest.Format, exportRequest.StartDate, exportRequest.EndDate);

            await Task.Delay(100); // Simuler le traitement
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error exporting audit logs");
            return false;
        }
    }

    // Méthodes privées
    private ProductApprovalAuditDto MapToDto(ProductApprovalAudit audit)
    {
        return new ProductApprovalAuditDto
        {
            Id = audit.Id,
            ProductId = audit.ProductId,
            ProductName = audit.ProductName,
            SellerId = audit.SellerId,
            SellerName = audit.SellerName,
            Action = audit.Action,
            PerformedBy = audit.PerformedBy,
            Notes = audit.Notes,
            Reason = audit.Reason,
            Timestamp = audit.Timestamp,
            IpAddress = audit.IpAddress,
            UserAgent = audit.UserAgent,
            AdditionalData = string.IsNullOrEmpty(audit.AdditionalData) 
                ? new Dictionary<string, object>() 
                : JsonSerializer.Deserialize<Dictionary<string, object>>(audit.AdditionalData) ?? new Dictionary<string, object>()
        };
    }

    private string CreateAdditionalData(Product product, ProductApprovalAction action)
    {
        var data = new Dictionary<string, object>
        {
            { "productPrice", product.Price },
            { "productCategory", product.CategoryId },
            { "productStatus", product.ApprovalStatus.ToString() },
            { "actionTimestamp", DateTime.UtcNow },
            { "productCreatedAt", product.CreatedAt }
        };

        return JsonSerializer.Serialize(data);
    }

    private string GetCurrentIpAddress()
    {
        // Dans une vraie application, on récupérerait l'IP depuis le contexte HTTP
        return "127.0.0.1";
    }

    private string GetCurrentUserAgent()
    {
        // Dans une vraie application, on récupérerait le User-Agent depuis le contexte HTTP
        return "NafaPlace-System";
    }
}

// Modèle pour l'audit en base de données
public class ProductApprovalAudit
{
    public int Id { get; set; }
    public int ProductId { get; set; }
    public string ProductName { get; set; } = string.Empty;
    public int SellerId { get; set; }
    public string SellerName { get; set; } = string.Empty;
    public ProductApprovalAction Action { get; set; }
    public string PerformedBy { get; set; } = string.Empty;
    public string? Notes { get; set; }
    public string? Reason { get; set; }
    public DateTime Timestamp { get; set; }
    public string? IpAddress { get; set; }
    public string? UserAgent { get; set; }
    public string? AdditionalData { get; set; }
}

// DTOs pour l'audit
public class ProductApprovalAuditDto
{
    public int Id { get; set; }
    public int ProductId { get; set; }
    public string ProductName { get; set; } = string.Empty;
    public int SellerId { get; set; }
    public string SellerName { get; set; } = string.Empty;
    public ProductApprovalAction Action { get; set; }
    public string PerformedBy { get; set; } = string.Empty;
    public string? Notes { get; set; }
    public string? Reason { get; set; }
    public DateTime Timestamp { get; set; }
    public string? IpAddress { get; set; }
    public string? UserAgent { get; set; }
    public Dictionary<string, object> AdditionalData { get; set; } = new();
}

public class ProductApprovalAuditStatsDto
{
    public int TotalActions { get; set; }
    public Dictionary<string, int> ActionsByType { get; set; } = new();
    public Dictionary<string, int> ActionsByUser { get; set; } = new();
    public Dictionary<DateTime, int> ActionsByDay { get; set; } = new();
    public Dictionary<string, int> MostActiveUsers { get; set; } = new();
    public List<ProductApprovalAuditDto> RecentActions { get; set; } = new();
}

public class ProductApprovalAuditSearchDto
{
    public int? ProductId { get; set; }
    public string? PerformedBy { get; set; }
    public ProductApprovalAction? Action { get; set; }
    public DateTime? StartDate { get; set; }
    public DateTime? EndDate { get; set; }
    public string? SearchTerm { get; set; }
    public int Page { get; set; } = 1;
    public int PageSize { get; set; } = 50;
    public string? SortBy { get; set; } = "Timestamp";
    public string? SortDirection { get; set; } = "desc";
}

public class ProductApprovalAuditExportDto
{
    public DateTime? StartDate { get; set; }
    public DateTime? EndDate { get; set; }
    public string Format { get; set; } = "csv"; // csv, excel, json
    public List<ProductApprovalAction>? Actions { get; set; }
    public List<string>? Users { get; set; }
    public string? FileName { get; set; }
}
