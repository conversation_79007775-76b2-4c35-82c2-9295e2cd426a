using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using NafaPlace.Analytics.Application.Interfaces;
using NafaPlace.Analytics.Application.DTOs;
using NafaPlace.Analytics.Infrastructure.Data;
using System.Text.Json;

namespace NafaPlace.Analytics.Infrastructure.Services;

public class AnalyticsService : IAnalyticsService
{
    private readonly AnalyticsDbContext _context;
    private readonly ILogger<AnalyticsService> _logger;
    private readonly HttpClient _httpClient;

    public AnalyticsService(
        AnalyticsDbContext context,
        ILogger<AnalyticsService> logger,
        HttpClient httpClient)
    {
        _context = context;
        _logger = logger;
        _httpClient = httpClient;
    }

    public async Task<SellerDashboardDto> GetSellerDashboardAsync(AnalyticsRequest request)
    {
        try
        {
            _logger.LogInformation("Generating seller dashboard for seller {SellerId}", request.SellerId);

            // Récupérer les données des différents services
            var ordersTask = GetOrdersDataAsync(request);
            var productsTask = GetProductsDataAsync(request);
            var customersTask = GetCustomersDataAsync(request);

            await Task.WhenAll(ordersTask, productsTask, customersTask);

            var orders = await ordersTask;
            var products = await productsTask;
            var customers = await customersTask;

            // Calculer les métriques
            var salesMetrics = CalculateSalesMetrics(orders, request);
            var orderMetrics = CalculateOrderMetrics(orders);
            var productMetrics = CalculateProductMetrics(products, orders);
            var customerMetrics = CalculateCustomerMetrics(customers, orders);

            // Générer les graphiques et listes
            var salesChart = GenerateSalesChart(orders, request);
            var topProducts = GetTopProducts(orders, products, 5);
            var recentOrders = GetRecentOrders(orders, 10);
            var inventoryAlerts = await GetInventoryAlertsAsync(request.SellerId);

            return new SellerDashboardDto
            {
                Sales = salesMetrics,
                Orders = orderMetrics,
                Products = productMetrics,
                Customers = customerMetrics,
                SalesChart = salesChart,
                TopProducts = topProducts,
                RecentOrders = recentOrders,
                InventoryAlerts = inventoryAlerts
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating seller dashboard for seller {SellerId}", request.SellerId);
            return new SellerDashboardDto();
        }
    }

    public async Task<AdminDashboardDto> GetAdminDashboardAsync(AnalyticsRequest request)
    {
        try
        {
            _logger.LogInformation("Generating admin dashboard");

            // Récupérer les données de tous les services
            var ordersTask = GetOrdersDataAsync(request);
            var productsTask = GetProductsDataAsync(request);
            var usersTask = GetUsersDataAsync(request);
            var sellersTask = GetSellersDataAsync(request);

            await Task.WhenAll(ordersTask, productsTask, usersTask, sellersTask);

            var orders = await ordersTask;
            var products = await productsTask;
            var users = await usersTask;
            var sellers = await sellersTask;

            // Calculer les métriques globales
            var platformMetrics = CalculatePlatformMetrics(sellers, users, orders);
            var salesMetrics = CalculateSalesMetrics(orders, request);
            var userMetrics = CalculateUserMetrics(users);
            var orderMetrics = CalculateOrderMetrics(orders);

            // Générer les analyses
            var salesChart = GenerateSalesChart(orders, request);
            var topSellers = GetTopSellers(sellers, orders, 10);
            var categoryPerformance = GetCategoryPerformance(orders, products);
            var regionPerformance = GetRegionPerformance(orders);

            return new AdminDashboardDto
            {
                Platform = platformMetrics,
                Sales = salesMetrics,
                Users = userMetrics,
                Orders = orderMetrics,
                SalesChart = salesChart,
                TopSellers = topSellers,
                CategoryPerformance = categoryPerformance,
                RegionPerformance = regionPerformance
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating admin dashboard");
            return new AdminDashboardDto();
        }
    }

    public async Task<SalesAnalyticsDto> GetSalesAnalyticsAsync(AnalyticsRequest request)
    {
        try
        {
            var orders = await GetOrdersDataAsync(request);
            var products = await GetProductsDataAsync(request);

            var salesOverTime = GenerateSalesChart(orders, request);
            var salesByCategory = GetSalesByCategory(orders, products);
            var salesByRegion = GetSalesByRegion(orders);
            var salesByPaymentMethod = GetSalesByPaymentMethod(orders);
            var summary = CalculateSalesMetrics(orders, request);
            var trends = CalculateSalesTrends(orders, request);

            return new SalesAnalyticsDto
            {
                SalesOverTime = salesOverTime,
                SalesByCategory = salesByCategory,
                SalesByRegion = salesByRegion,
                SalesByPaymentMethod = salesByPaymentMethod,
                Summary = summary,
                Trends = trends
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating sales analytics");
            return new SalesAnalyticsDto();
        }
    }

    public async Task<ProductPerformanceDto> GetProductPerformanceAsync(AnalyticsRequest request)
    {
        try
        {
            var orders = await GetOrdersDataAsync(request);
            var products = await GetProductsDataAsync(request);

            var topSellingProducts = GetTopProducts(orders, products, request.Limit);
            var topRevenueProducts = GetTopRevenueProducts(orders, products, request.Limit);
            var lowPerformingProducts = GetLowPerformingProducts(orders, products, request.Limit);
            var trendingProducts = GetTrendingProducts(orders, products, request.Limit);
            var categoryPerformance = GetCategoryPerformance(orders, products);
            var summary = CalculateProductMetrics(products, orders);

            return new ProductPerformanceDto
            {
                TopSellingProducts = topSellingProducts,
                TopRevenueProducts = topRevenueProducts,
                LowPerformingProducts = lowPerformingProducts,
                TrendingProducts = trendingProducts,
                CategoryPerformance = categoryPerformance,
                Summary = summary
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating product performance analytics");
            return new ProductPerformanceDto();
        }
    }

    public async Task<CustomerAnalyticsDto> GetCustomerAnalyticsAsync(AnalyticsRequest request)
    {
        try
        {
            var customers = await GetCustomersDataAsync(request);
            var orders = await GetOrdersDataAsync(request);

            var customerSegments = GetCustomerSegments(customers, orders);
            var customerAcquisition = GetCustomerAcquisitionChart(customers, request);
            var customerRetention = GetCustomerRetentionChart(customers, orders, request);
            var topCustomers = GetTopCustomers(customers, orders, request.Limit);
            var summary = CalculateCustomerMetrics(customers, orders);
            var behaviorInsights = GetCustomerBehaviorInsights(customers, orders);

            return new CustomerAnalyticsDto
            {
                CustomerSegments = customerSegments,
                CustomerAcquisition = customerAcquisition,
                CustomerRetention = customerRetention,
                TopCustomers = topCustomers,
                Summary = summary,
                BehaviorInsights = behaviorInsights
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating customer analytics");
            return new CustomerAnalyticsDto();
        }
    }

    public async Task<FinancialAnalyticsDto> GetFinancialAnalyticsAsync(AnalyticsRequest request)
    {
        try
        {
            var orders = await GetOrdersDataAsync(request);
            var expenses = await GetExpensesDataAsync(request);
            var taxes = await GetTaxesDataAsync(request);

            var revenueOverTime = GenerateRevenueChart(orders, request);
            var profitMargins = CalculateProfitMargins(orders, expenses, request);
            var summary = CalculateFinancialMetrics(orders, expenses, taxes);
            var cashFlow = CalculateCashFlow(orders, expenses, request);

            return new FinancialAnalyticsDto
            {
                RevenueOverTime = revenueOverTime,
                ProfitMargins = profitMargins,
                Expenses = expenses,
                Taxes = taxes,
                Summary = summary,
                CashFlow = cashFlow
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating financial analytics");
            return new FinancialAnalyticsDto();
        }
    }

    public async Task<InventoryAnalyticsDto> GetInventoryAnalyticsAsync(AnalyticsRequest request)
    {
        try
        {
            var products = await GetProductsDataAsync(request);
            var stockMovements = await GetStockMovementsAsync(request);

            var lowStockProducts = GetLowStockProducts(products);
            var outOfStockProducts = GetOutOfStockProducts(products);
            var overstockedProducts = GetOverstockedProducts(products);
            var stockMovementsChart = GenerateStockMovementsChart(stockMovements, request);
            var summary = CalculateInventoryMetrics(products);
            var alerts = GenerateStockAlerts(products);

            return new InventoryAnalyticsDto
            {
                LowStockProducts = lowStockProducts,
                OutOfStockProducts = outOfStockProducts,
                OverstockedProducts = overstockedProducts,
                StockMovements = stockMovementsChart,
                Summary = summary,
                Alerts = alerts
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating inventory analytics");
            return new InventoryAnalyticsDto();
        }
    }

    public async Task<ComparisonAnalyticsDto> GetComparisonAnalyticsAsync(ComparisonRequest request)
    {
        try
        {
            var currentOrders = await GetOrdersDataAsync(request.CurrentPeriod);
            var previousOrders = await GetOrdersDataAsync(request.PreviousPeriod);

            var currentMetrics = CalculateSalesMetrics(currentOrders, request.CurrentPeriod);
            var previousMetrics = CalculateSalesMetrics(previousOrders, request.PreviousPeriod);
            var comparison = CalculateComparisonMetrics(currentMetrics, previousMetrics);
            var comparisonChart = GenerateComparisonChart(currentOrders, previousOrders, request);

            return new ComparisonAnalyticsDto
            {
                CurrentPeriod = currentMetrics,
                PreviousPeriod = previousMetrics,
                Comparison = comparison,
                ComparisonChart = comparisonChart
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating comparison analytics");
            return new ComparisonAnalyticsDto();
        }
    }

    public async Task<RealTimeMetricsDto> GetRealTimeMetricsAsync(int? sellerId = null)
    {
        try
        {
            var today = DateTime.UtcNow.Date;
            var request = new AnalyticsRequest
            {
                SellerId = sellerId,
                StartDate = today,
                EndDate = DateTime.UtcNow
            };

            var todayOrders = await GetOrdersDataAsync(request);
            var onlineUsers = await GetOnlineUsersCountAsync();
            var pendingOrders = await GetPendingOrdersCountAsync(sellerId);
            var lowStockAlerts = await GetLowStockAlertsCountAsync(sellerId);
            var recentActivities = await GetRecentActivitiesAsync(sellerId, 10);

            return new RealTimeMetricsDto
            {
                OnlineUsers = onlineUsers,
                TodayOrders = todayOrders.Count,
                TodayRevenue = todayOrders.Sum(o => o.TotalAmount),
                PendingOrders = pendingOrders,
                LowStockAlerts = lowStockAlerts,
                RecentActivities = recentActivities,
                LastUpdated = DateTime.UtcNow
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting real-time metrics");
            return new RealTimeMetricsDto();
        }
    }

    public async Task<byte[]> ExportAnalyticsAsync(ExportRequest request)
    {
        try
        {
            _logger.LogInformation("Exporting analytics data in {Format} format", request.Format);

            var analyticsRequest = new AnalyticsRequest
            {
                SellerId = request.SellerId,
                StartDate = request.StartDate,
                EndDate = request.EndDate
            };

            // Récupérer les données selon le type de rapport
            var data = request.ReportType.ToLower() switch
            {
                "dashboard" => await GetSellerDashboardAsync(analyticsRequest),
                "sales" => await GetSalesAnalyticsAsync(analyticsRequest),
                "products" => await GetProductPerformanceAsync(analyticsRequest),
                "customers" => await GetCustomerAnalyticsAsync(analyticsRequest),
                _ => await GetSellerDashboardAsync(analyticsRequest)
            };

            // Exporter selon le format demandé
            return request.Format.ToLower() switch
            {
                "csv" => ExportToCsv(data, request),
                "excel" => ExportToExcel(data, request),
                "pdf" => ExportToPdf(data, request),
                _ => ExportToExcel(data, request)
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error exporting analytics data");
            throw;
        }
    }

    // Méthodes privées pour récupérer les données des autres services
    private async Task<List<OrderDataDto>> GetOrdersDataAsync(AnalyticsRequest request)
    {
        try
        {
            var queryParams = $"?startDate={request.StartDate:yyyy-MM-dd}&endDate={request.EndDate:yyyy-MM-dd}";
            if (request.SellerId.HasValue)
                queryParams += $"&sellerId={request.SellerId}";

            var response = await _httpClient.GetAsync($"/api/orders/analytics{queryParams}");
            if (response.IsSuccessStatusCode)
            {
                var json = await response.Content.ReadAsStringAsync();
                return JsonSerializer.Deserialize<List<OrderDataDto>>(json, new JsonSerializerOptions { PropertyNameCaseInsensitive = true }) ?? new List<OrderDataDto>();
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error fetching orders data");
        }

        return new List<OrderDataDto>();
    }

    private async Task<List<ProductDataDto>> GetProductsDataAsync(AnalyticsRequest request)
    {
        try
        {
            var queryParams = "";
            if (request.SellerId.HasValue)
                queryParams = $"?sellerId={request.SellerId}";

            var response = await _httpClient.GetAsync($"/api/products/analytics{queryParams}");
            if (response.IsSuccessStatusCode)
            {
                var json = await response.Content.ReadAsStringAsync();
                return JsonSerializer.Deserialize<List<ProductDataDto>>(json, new JsonSerializerOptions { PropertyNameCaseInsensitive = true }) ?? new List<ProductDataDto>();
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error fetching products data");
        }

        return new List<ProductDataDto>();
    }

    private async Task<List<CustomerDataDto>> GetCustomersDataAsync(AnalyticsRequest request)
    {
        try
        {
            var queryParams = $"?startDate={request.StartDate:yyyy-MM-dd}&endDate={request.EndDate:yyyy-MM-dd}";
            if (request.SellerId.HasValue)
                queryParams += $"&sellerId={request.SellerId}";

            var response = await _httpClient.GetAsync($"/api/customers/analytics{queryParams}");
            if (response.IsSuccessStatusCode)
            {
                var json = await response.Content.ReadAsStringAsync();
                return JsonSerializer.Deserialize<List<CustomerDataDto>>(json, new JsonSerializerOptions { PropertyNameCaseInsensitive = true }) ?? new List<CustomerDataDto>();
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error fetching customers data");
        }

        return new List<CustomerDataDto>();
    }

    private async Task<List<UserDataDto>> GetUsersDataAsync(AnalyticsRequest request)
    {
        try
        {
            var queryParams = $"?startDate={request.StartDate:yyyy-MM-dd}&endDate={request.EndDate:yyyy-MM-dd}";
            var response = await _httpClient.GetAsync($"/api/users/analytics{queryParams}");
            if (response.IsSuccessStatusCode)
            {
                var json = await response.Content.ReadAsStringAsync();
                return JsonSerializer.Deserialize<List<UserDataDto>>(json, new JsonSerializerOptions { PropertyNameCaseInsensitive = true }) ?? new List<UserDataDto>();
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error fetching users data");
        }

        return new List<UserDataDto>();
    }

    private async Task<List<SellerDataDto>> GetSellersDataAsync(AnalyticsRequest request)
    {
        try
        {
            var queryParams = $"?startDate={request.StartDate:yyyy-MM-dd}&endDate={request.EndDate:yyyy-MM-dd}";
            var response = await _httpClient.GetAsync($"/api/sellers/analytics{queryParams}");
            if (response.IsSuccessStatusCode)
            {
                var json = await response.Content.ReadAsStringAsync();
                return JsonSerializer.Deserialize<List<SellerDataDto>>(json, new JsonSerializerOptions { PropertyNameCaseInsensitive = true }) ?? new List<SellerDataDto>();
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error fetching sellers data");
        }

        return new List<SellerDataDto>();
    }

    // Méthodes de calcul des métriques (à implémenter)
    private SalesMetricsDto CalculateSalesMetrics(List<OrderDataDto> orders, AnalyticsRequest request)
    {
        var completedOrders = orders.Where(o => o.Status == "Completed").ToList();
        var totalRevenue = completedOrders.Sum(o => o.TotalAmount);
        var totalOrders = completedOrders.Count;
        var averageOrderValue = totalOrders > 0 ? totalRevenue / totalOrders : 0;

        // Calculer la croissance (comparaison avec la période précédente)
        var previousPeriodStart = request.StartDate.AddDays(-(request.EndDate - request.StartDate).Days);
        var previousPeriodEnd = request.StartDate;
        
        // Pour simplifier, on utilise des valeurs simulées pour la croissance
        var revenueGrowth = 12.5m; // À calculer réellement
        var ordersGrowth = 8.3m;
        var aovGrowth = 4.2m;

        return new SalesMetricsDto
        {
            TotalRevenue = totalRevenue,
            RevenueGrowth = revenueGrowth,
            TotalOrders = totalOrders,
            OrdersGrowth = ordersGrowth,
            AverageOrderValue = averageOrderValue,
            AovGrowth = aovGrowth,
            ConversionRate = 3.8m, // À calculer à partir des données de trafic
            ConversionGrowth = -0.5m,
            RefundRate = 2.1m,
            RefundAmount = orders.Where(o => o.Status == "Refunded").Sum(o => o.TotalAmount)
        };
    }

    // Autres méthodes de calcul à implémenter...
    // (Les méthodes suivantes seraient trop longues pour ce fichier, elles seront dans des fichiers séparés)
}
