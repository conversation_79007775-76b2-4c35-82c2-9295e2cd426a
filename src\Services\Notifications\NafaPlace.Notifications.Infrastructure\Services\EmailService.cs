using Microsoft.Extensions.Logging;
using NafaPlace.Notifications.Application.Services;

namespace NafaPlace.Notifications.Infrastructure.Services;

public class EmailService : IEmailService
{
    private readonly ILogger<EmailService> _logger;

    public EmailService(ILogger<EmailService> logger)
    {
        _logger = logger;
    }

    public async Task<bool> SendEmailAsync(string to, string subject, string body, bool isHtml = true)
    {
        try
        {
            // Mode développement - simulation d'envoi d'email
            _logger.LogInformation("📧 [SIMULATION] Sending email to {To}: {Subject}", to, subject);
            _logger.LogDebug("Email body preview: {BodyPreview}", body.Length > 100 ? body[..100] + "..." : body);

            // Simuler un délai réseau réaliste
            await Task.Delay(Random.Shared.Next(50, 200));

            // Simuler occasionnellement des échecs pour tester la robustesse
            if (Random.Shared.Next(1, 100) <= 2) // 2% de chance d'échec
            {
                throw new Exception("Simulated email delivery failure");
            }

            _logger.LogInformation("✅ [SIMULATION] Email sent successfully to {To}", to);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ [SIMULATION] Failed to send email to {To}", to);
            return false;
        }
    }

    public async Task<bool> SendEmailWithTemplateAsync(string to, string templateCode, Dictionary<string, object> variables)
    {
        try
        {
            _logger.LogInformation("📧 Sending templated email to {To} with template {TemplateCode}", to, templateCode);
            
            // Simulation d'envoi avec template
            await Task.Delay(100);
            
            _logger.LogInformation("✅ Templated email sent successfully to {To}", to);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ Failed to send templated email to {To}", to);
            return false;
        }
    }

    public async Task<bool> SendBulkEmailAsync(List<string> recipients, string subject, string body, bool isHtml = true)
    {
        var successCount = 0;
        
        foreach (var recipient in recipients)
        {
            if (await SendEmailAsync(recipient, subject, body, isHtml))
            {
                successCount++;
            }
        }

        _logger.LogInformation("📧 Bulk email sent: {SuccessCount}/{TotalCount}", successCount, recipients.Count);
        return successCount == recipients.Count;
    }

    public async Task<bool> IsEmailValidAsync(string email)
    {
        await Task.CompletedTask;
        
        // Validation basique d'email
        if (string.IsNullOrWhiteSpace(email))
            return false;

        try
        {
            var addr = new System.Net.Mail.MailAddress(email);
            return addr.Address == email;
        }
        catch
        {
            return false;
        }
    }
}
