@using NafaPlace.Web.Services
@inject IJSRuntime JSRuntime

<div class="modal fade @(_showModal ? "show" : "")" style="display: @(_showModal ? "block" : "none")" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-shopping-cart me-2"></i>
                    Fusion des paniers
                </h5>
                <button type="button" class="btn-close" @onclick="CloseModal"></button>
            </div>
            <div class="modal-body">
                @if (_isLoading)
                {
                    <div class="text-center py-4">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Analyse en cours...</span>
                        </div>
                        <p class="mt-2">Analyse des conflits de panier...</p>
                    </div>
                }
                else if (_syncResult != null)
                {
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        Nous avons détecté des articles dans votre panier invité. Comment souhaitez-vous procéder ?
                    </div>

                    @if (_syncResult.ConflictingItems.Any())
                    {
                        <div class="conflicts-section mb-4">
                            <h6 class="text-warning">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                Articles en conflit (@_syncResult.ConflictingItems.Count)
                            </h6>
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>Produit</th>
                                            <th>Panier invité</th>
                                            <th>Votre panier</th>
                                            <th>Action proposée</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach (var conflict in _syncResult.ConflictingItems)
                                        {
                                            <tr>
                                                <td>@conflict.ProductName</td>
                                                <td>
                                                    <span class="badge bg-secondary">@conflict.GuestQuantity</span>
                                                </td>
                                                <td>
                                                    <span class="badge bg-primary">@conflict.UserQuantity</span>
                                                </td>
                                                <td>
                                                    <span class="text-success">
                                                        @GetActionDescription(_selectedStrategy, conflict)
                                                    </span>
                                                </td>
                                            </tr>
                                        }
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    }

                    @if (_syncResult.NewItems.Any())
                    {
                        <div class="new-items-section mb-4">
                            <h6 class="text-success">
                                <i class="fas fa-plus-circle me-2"></i>
                                Nouveaux articles (@_syncResult.NewItems.Count)
                            </h6>
                            <div class="row">
                                @foreach (var item in _syncResult.NewItems)
                                {
                                    <div class="col-md-6 mb-2">
                                        <div class="card card-body py-2">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <span>@item.ProductName</span>
                                                <span class="badge bg-success">+@item.Quantity</span>
                                            </div>
                                        </div>
                                    </div>
                                }
                            </div>
                        </div>
                    }

                    <div class="strategy-selection">
                        <h6>Choisissez une stratégie de fusion :</h6>
                        <div class="list-group">
                            <label class="list-group-item">
                                <input class="form-check-input me-2" type="radio" name="strategy" 
                                       value="@CartConflictStrategy.MergeAddQuantities" 
                                       @onchange="(e) => OnStrategyChanged(CartConflictStrategy.MergeAddQuantities)"
                                       checked="@(_selectedStrategy == CartConflictStrategy.MergeAddQuantities)">
                                <div>
                                    <strong>Fusionner en additionnant les quantités</strong>
                                    <small class="text-muted d-block">
                                        Ajouter les quantités du panier invité à votre panier existant
                                    </small>
                                </div>
                            </label>
                            
                            <label class="list-group-item">
                                <input class="form-check-input me-2" type="radio" name="strategy" 
                                       value="@CartConflictStrategy.ReplaceUserCart" 
                                       @onchange="(e) => OnStrategyChanged(CartConflictStrategy.ReplaceUserCart)"
                                       checked="@(_selectedStrategy == CartConflictStrategy.ReplaceUserCart)">
                                <div>
                                    <strong>Remplacer par le panier invité</strong>
                                    <small class="text-muted d-block">
                                        Vider votre panier actuel et utiliser uniquement le panier invité
                                    </small>
                                </div>
                            </label>
                            
                            <label class="list-group-item">
                                <input class="form-check-input me-2" type="radio" name="strategy" 
                                       value="@CartConflictStrategy.KeepUserCart" 
                                       @onchange="(e) => OnStrategyChanged(CartConflictStrategy.KeepUserCart)"
                                       checked="@(_selectedStrategy == CartConflictStrategy.KeepUserCart)">
                                <div>
                                    <strong>Garder mon panier actuel</strong>
                                    <small class="text-muted d-block">
                                        Ignorer le panier invité et garder uniquement votre panier existant
                                    </small>
                                </div>
                            </label>
                            
                            <label class="list-group-item">
                                <input class="form-check-input me-2" type="radio" name="strategy" 
                                       value="@CartConflictStrategy.KeepNewerItems" 
                                       @onchange="(e) => OnStrategyChanged(CartConflictStrategy.KeepNewerItems)"
                                       checked="@(_selectedStrategy == CartConflictStrategy.KeepNewerItems)">
                                <div>
                                    <strong>Garder les articles les plus récents</strong>
                                    <small class="text-muted d-block">
                                        Fusionner en gardant les articles ajoutés le plus récemment
                                    </small>
                                </div>
                            </label>
                        </div>
                    </div>

                    <div class="summary-section mt-4">
                        <div class="alert alert-light">
                            <h6>Résumé de la fusion :</h6>
                            <ul class="mb-0">
                                <li>Articles en conflit : @_syncResult.ConflictingItems.Count</li>
                                <li>Nouveaux articles : @_syncResult.NewItems.Count</li>
                                <li>Stratégie : @GetStrategyDisplayName(_selectedStrategy)</li>
                            </ul>
                        </div>
                    </div>
                }
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" @onclick="CloseModal">
                    Annuler
                </button>
                <button type="button" class="btn btn-primary" @onclick="ApplyStrategy" disabled="@_isProcessing">
                    @if (_isProcessing)
                    {
                        <span class="spinner-border spinner-border-sm me-2"></span>
                    }
                    <i class="fas fa-check me-2"></i>
                    Appliquer la fusion
                </button>
            </div>
        </div>
    </div>
</div>

@if (_showModal)
{
    <div class="modal-backdrop fade show"></div>
}

@code {
    [Parameter] public EventCallback<CartConflictStrategy> OnStrategySelected { get; set; }
    [Parameter] public EventCallback OnCancelled { get; set; }

    private bool _showModal = false;
    private bool _isLoading = false;
    private bool _isProcessing = false;
    private CartSyncResult? _syncResult;
    private CartConflictStrategy _selectedStrategy = CartConflictStrategy.MergeAddQuantities;

    public async Task ShowAsync(CartSyncResult syncResult)
    {
        _syncResult = syncResult;
        _showModal = true;
        _isLoading = false;
        StateHasChanged();
        
        // Empêcher le scroll du body
        await JSRuntime.InvokeVoidAsync("document.body.classList.add", "modal-open");
    }

    public async Task ShowLoadingAsync()
    {
        _isLoading = true;
        _showModal = true;
        StateHasChanged();
        
        await JSRuntime.InvokeVoidAsync("document.body.classList.add", "modal-open");
    }

    private async Task CloseModal()
    {
        _showModal = false;
        _isLoading = false;
        _isProcessing = false;
        StateHasChanged();
        
        // Restaurer le scroll du body
        await JSRuntime.InvokeVoidAsync("document.body.classList.remove", "modal-open");
        
        await OnCancelled.InvokeAsync();
    }

    private void OnStrategyChanged(CartConflictStrategy strategy)
    {
        _selectedStrategy = strategy;
        StateHasChanged();
    }

    private async Task ApplyStrategy()
    {
        _isProcessing = true;
        StateHasChanged();

        try
        {
            await OnStrategySelected.InvokeAsync(_selectedStrategy);
            await CloseModal();
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("showToast", "Erreur lors de la fusion du panier", "error");
            Console.WriteLine($"Erreur fusion panier: {ex.Message}");
        }
        finally
        {
            _isProcessing = false;
        }
    }

    private string GetActionDescription(CartConflictStrategy strategy, CartItemConflict conflict)
    {
        return strategy switch
        {
            CartConflictStrategy.MergeAddQuantities => $"Total: {conflict.GuestQuantity + conflict.UserQuantity}",
            CartConflictStrategy.ReplaceUserCart => $"Remplacer par: {conflict.GuestQuantity}",
            CartConflictStrategy.KeepUserCart => $"Garder: {conflict.UserQuantity}",
            CartConflictStrategy.KeepNewerItems => "Garder le plus récent",
            _ => "Action inconnue"
        };
    }

    private string GetStrategyDisplayName(CartConflictStrategy strategy)
    {
        return strategy switch
        {
            CartConflictStrategy.MergeAddQuantities => "Fusionner en additionnant",
            CartConflictStrategy.ReplaceUserCart => "Remplacer par le panier invité",
            CartConflictStrategy.KeepUserCart => "Garder le panier actuel",
            CartConflictStrategy.KeepNewerItems => "Garder les plus récents",
            _ => "Stratégie inconnue"
        };
    }
}

<style>
    .modal {
        z-index: 1055;
    }

    .modal-backdrop {
        z-index: 1050;
    }

    .conflicts-section .table th {
        background-color: #f8f9fa;
        border-top: none;
    }

    .list-group-item {
        cursor: pointer;
        transition: background-color 0.2s;
    }

    .list-group-item:hover {
        background-color: #f8f9fa;
    }

    .list-group-item input[type="radio"]:checked ~ div {
        color: #0d6efd;
    }

    .summary-section .alert {
        border-left: 4px solid #0d6efd;
    }

    .new-items-section .card {
        border: 1px solid #d4edda;
        background-color: #f8fff9;
    }

    .conflicts-section {
        border: 1px solid #fff3cd;
        background-color: #fffbf0;
        padding: 1rem;
        border-radius: 0.375rem;
    }
</style>
