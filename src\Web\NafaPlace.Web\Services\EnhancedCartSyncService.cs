using Microsoft.JSInterop;
using Microsoft.Extensions.Logging;
using NafaPlace.Cart.Application.Services;
using NafaPlace.Cart.Application.DTOs;
using System.Text.Json;

namespace NafaPlace.Web.Services;

public interface IEnhancedCartSyncService
{
    Task<bool> SyncCartOnLoginAsync(string authenticatedUserId);
    Task<bool> SyncCartOnLogoutAsync();
    Task<bool> BackupCurrentCartAsync();
    Task<bool> RestoreCartAsync(string userId);
    Task<string> GetOrCreateGuestIdAsync();
    Task<bool> ClearGuestDataAsync();
    Task<CartSyncResult> ValidateCartSyncAsync(string userId);
    Task<bool> HandleCartConflictAsync(string guestUserId, string authenticatedUserId, CartConflictStrategy strategy);
    event Action<CartSyncEventArgs>? CartSyncCompleted;
}

public class EnhancedCartSyncService : IEnhancedCartSyncService
{
    private readonly IJSRuntime _jsRuntime;
    private readonly ICartService _cartService;
    private readonly ILogger<EnhancedCartSyncService> _logger;
    private readonly CartSyncSettings _settings;

    public event Action<CartSyncEventArgs>? CartSyncCompleted;

    public EnhancedCartSyncService(
        IJSRuntime jsRuntime,
        ICartService cartService,
        ILogger<EnhancedCartSyncService> logger,
        CartSyncSettings settings)
    {
        _jsRuntime = jsRuntime;
        _cartService = cartService;
        _logger = logger;
        _settings = settings;
    }

    public async Task<bool> SyncCartOnLoginAsync(string authenticatedUserId)
    {
        try
        {
            _logger.LogInformation("Starting cart sync on login for user {UserId}", authenticatedUserId);

            // Récupérer l'ID invité
            var guestId = await GetGuestIdFromStorageAsync();
            if (string.IsNullOrEmpty(guestId))
            {
                _logger.LogDebug("No guest cart found for sync");
                return false;
            }

            // Récupérer les paniers
            var guestCart = await _cartService.GetCartAsync(guestId);
            var userCart = await _cartService.GetCartAsync(authenticatedUserId);

            // Vérifier s'il y a des articles à fusionner
            if (guestCart?.Items?.Any() != true)
            {
                _logger.LogDebug("Guest cart is empty, no sync needed");
                await ClearGuestDataAsync();
                return false;
            }

            // Analyser les conflits potentiels
            var syncResult = await AnalyzeCartConflictsAsync(guestCart, userCart);
            
            // Appliquer la stratégie de résolution selon la configuration
            var success = await HandleCartConflictAsync(guestId, authenticatedUserId, _settings.DefaultConflictStrategy);

            if (success)
            {
                // Nettoyer les données invité
                await ClearGuestDataAsync();
                
                // Déclencher l'événement de synchronisation
                CartSyncCompleted?.Invoke(new CartSyncEventArgs
                {
                    UserId = authenticatedUserId,
                    SyncType = CartSyncType.Login,
                    Success = true,
                    ItemsMerged = syncResult.ConflictingItems.Count + syncResult.NewItems.Count,
                    ConflictsResolved = syncResult.ConflictingItems.Count
                });

                _logger.LogInformation("Cart sync completed successfully for user {UserId}", authenticatedUserId);
            }

            return success;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error syncing cart on login for user {UserId}", authenticatedUserId);
            
            CartSyncCompleted?.Invoke(new CartSyncEventArgs
            {
                UserId = authenticatedUserId,
                SyncType = CartSyncType.Login,
                Success = false,
                ErrorMessage = ex.Message
            });
            
            return false;
        }
    }

    public async Task<bool> SyncCartOnLogoutAsync()
    {
        try
        {
            _logger.LogInformation("Starting cart sync on logout");

            // Créer un nouvel ID invité pour la session
            var newGuestId = await CreateNewGuestIdAsync();
            
            // Optionnellement, sauvegarder le panier actuel comme backup
            if (_settings.BackupOnLogout)
            {
                await BackupCurrentCartAsync();
            }

            _logger.LogDebug("Cart sync on logout completed with new guest ID: {GuestId}", newGuestId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error syncing cart on logout");
            return false;
        }
    }

    public async Task<bool> BackupCurrentCartAsync()
    {
        try
        {
            var currentUserId = await GetCurrentUserIdAsync();
            if (string.IsNullOrEmpty(currentUserId))
                return false;

            var cart = await _cartService.GetCartAsync(currentUserId);
            if (cart?.Items?.Any() != true)
                return false;

            var backupKey = $"cart_backup_{DateTime.UtcNow:yyyyMMdd_HHmmss}";
            var backupData = new CartBackup
            {
                UserId = currentUserId,
                Cart = cart,
                BackupDate = DateTime.UtcNow,
                ExpiresAt = DateTime.UtcNow.AddDays(_settings.BackupRetentionDays)
            };

            await _jsRuntime.InvokeVoidAsync("localStorage.setItem", backupKey, JsonSerializer.Serialize(backupData));
            
            _logger.LogDebug("Cart backup created for user {UserId}", currentUserId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error backing up current cart");
            return false;
        }
    }

    public async Task<bool> RestoreCartAsync(string userId)
    {
        try
        {
            // Chercher les backups disponibles
            var backups = await GetAvailableBackupsAsync();
            var latestBackup = backups
                .Where(b => b.UserId == userId && b.ExpiresAt > DateTime.UtcNow)
                .OrderByDescending(b => b.BackupDate)
                .FirstOrDefault();

            if (latestBackup == null)
            {
                _logger.LogDebug("No valid backup found for user {UserId}", userId);
                return false;
            }

            // Restaurer le panier
            foreach (var item in latestBackup.Cart.Items)
            {
                var cartItem = new AddToCartRequest
                {
                    ProductId = item.ProductId,
                    Quantity = item.Quantity,
                    VariantId = item.VariantId
                };

                await _cartService.AddItemToCartAsync(userId, cartItem);
            }

            _logger.LogInformation("Cart restored from backup for user {UserId} with {ItemCount} items", 
                userId, latestBackup.Cart.Items.Count);
            
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error restoring cart for user {UserId}", userId);
            return false;
        }
    }

    public async Task<string> GetOrCreateGuestIdAsync()
    {
        try
        {
            var guestId = await GetGuestIdFromStorageAsync();
            
            if (string.IsNullOrEmpty(guestId))
            {
                guestId = await CreateNewGuestIdAsync();
            }

            return guestId;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting or creating guest ID");
            return $"guest_{Guid.NewGuid():N}";
        }
    }

    public async Task<bool> ClearGuestDataAsync()
    {
        try
        {
            await _jsRuntime.InvokeVoidAsync("localStorage.removeItem", "guestUserId");
            await _jsRuntime.InvokeVoidAsync("localStorage.removeItem", "guestCartTimestamp");
            
            _logger.LogDebug("Guest data cleared from localStorage");
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error clearing guest data");
            return false;
        }
    }

    public async Task<CartSyncResult> ValidateCartSyncAsync(string userId)
    {
        try
        {
            var guestId = await GetGuestIdFromStorageAsync();
            if (string.IsNullOrEmpty(guestId))
            {
                return new CartSyncResult { IsValid = true, RequiresSync = false };
            }

            var guestCart = await _cartService.GetCartAsync(guestId);
            var userCart = await _cartService.GetCartAsync(userId);

            return await AnalyzeCartConflictsAsync(guestCart, userCart);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating cart sync for user {UserId}", userId);
            return new CartSyncResult { IsValid = false, ErrorMessage = ex.Message };
        }
    }

    public async Task<bool> HandleCartConflictAsync(string guestUserId, string authenticatedUserId, CartConflictStrategy strategy)
    {
        try
        {
            var guestCart = await _cartService.GetCartAsync(guestUserId);
            var userCart = await _cartService.GetCartAsync(authenticatedUserId);

            if (guestCart?.Items?.Any() != true)
                return false;

            switch (strategy)
            {
                case CartConflictStrategy.MergeAddQuantities:
                    return await MergeCartsAddQuantitiesAsync(guestCart, authenticatedUserId);
                
                case CartConflictStrategy.ReplaceUserCart:
                    return await ReplaceUserCartAsync(guestCart, authenticatedUserId);
                
                case CartConflictStrategy.KeepUserCart:
                    return await ClearGuestCartAsync(guestUserId);
                
                case CartConflictStrategy.KeepNewerItems:
                    return await MergeCartsKeepNewerAsync(guestCart, userCart, authenticatedUserId);
                
                default:
                    return await MergeCartsAddQuantitiesAsync(guestCart, authenticatedUserId);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error handling cart conflict");
            return false;
        }
    }

    // Méthodes privées
    private async Task<string?> GetGuestIdFromStorageAsync()
    {
        try
        {
            return await _jsRuntime.InvokeAsync<string>("localStorage.getItem", "guestUserId");
        }
        catch
        {
            return null;
        }
    }

    private async Task<string> CreateNewGuestIdAsync()
    {
        var guestId = $"guest_{Guid.NewGuid():N}";
        var timestamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds().ToString();
        
        await _jsRuntime.InvokeVoidAsync("localStorage.setItem", "guestUserId", guestId);
        await _jsRuntime.InvokeVoidAsync("localStorage.setItem", "guestCartTimestamp", timestamp);
        
        return guestId;
    }

    private async Task<string?> GetCurrentUserIdAsync()
    {
        try
        {
            return await _jsRuntime.InvokeAsync<string>("getCurrentUserId");
        }
        catch
        {
            return null;
        }
    }

    private async Task<CartSyncResult> AnalyzeCartConflictsAsync(ShoppingCartDto guestCart, ShoppingCartDto userCart)
    {
        var result = new CartSyncResult { IsValid = true };

        if (guestCart?.Items?.Any() != true)
        {
            result.RequiresSync = false;
            return result;
        }

        result.RequiresSync = true;

        foreach (var guestItem in guestCart.Items)
        {
            var existingItem = userCart?.Items?.FirstOrDefault(i => 
                i.ProductId == guestItem.ProductId && i.VariantId == guestItem.VariantId);

            if (existingItem != null)
            {
                result.ConflictingItems.Add(new CartItemConflict
                {
                    ProductId = guestItem.ProductId,
                    VariantId = guestItem.VariantId,
                    GuestQuantity = guestItem.Quantity,
                    UserQuantity = existingItem.Quantity,
                    ProductName = guestItem.ProductName
                });
            }
            else
            {
                result.NewItems.Add(guestItem);
            }
        }

        return result;
    }

    private async Task<bool> MergeCartsAddQuantitiesAsync(ShoppingCartDto guestCart, string userId)
    {
        foreach (var item in guestCart.Items)
        {
            var cartItem = new AddToCartRequest
            {
                ProductId = item.ProductId,
                Quantity = item.Quantity,
                VariantId = item.VariantId
            };

            await _cartService.AddItemToCartAsync(userId, cartItem);
        }
        return true;
    }

    private async Task<bool> ReplaceUserCartAsync(ShoppingCartDto guestCart, string userId)
    {
        await _cartService.ClearCartAsync(userId);
        return await MergeCartsAddQuantitiesAsync(guestCart, userId);
    }

    private async Task<bool> ClearGuestCartAsync(string guestUserId)
    {
        return await _cartService.ClearCartAsync(guestUserId);
    }

    private async Task<bool> MergeCartsKeepNewerAsync(ShoppingCartDto guestCart, ShoppingCartDto userCart, string userId)
    {
        // Logique pour garder les articles les plus récents
        // Pour simplifier, on utilise la stratégie d'ajout des quantités
        return await MergeCartsAddQuantitiesAsync(guestCart, userId);
    }

    private async Task<List<CartBackup>> GetAvailableBackupsAsync()
    {
        try
        {
            var backups = new List<CartBackup>();
            var keys = await _jsRuntime.InvokeAsync<string[]>("getLocalStorageKeys");
            
            foreach (var key in keys.Where(k => k.StartsWith("cart_backup_")))
            {
                var backupData = await _jsRuntime.InvokeAsync<string>("localStorage.getItem", key);
                if (!string.IsNullOrEmpty(backupData))
                {
                    var backup = JsonSerializer.Deserialize<CartBackup>(backupData);
                    if (backup != null)
                    {
                        backups.Add(backup);
                    }
                }
            }

            return backups;
        }
        catch
        {
            return new List<CartBackup>();
        }
    }
}

// Classes de support
public class CartSyncSettings
{
    public CartConflictStrategy DefaultConflictStrategy { get; set; } = CartConflictStrategy.MergeAddQuantities;
    public bool BackupOnLogout { get; set; } = true;
    public int BackupRetentionDays { get; set; } = 7;
    public bool AutoSyncOnLogin { get; set; } = true;
    public bool ShowConflictDialog { get; set; } = false;
}

public enum CartConflictStrategy
{
    MergeAddQuantities,
    ReplaceUserCart,
    KeepUserCart,
    KeepNewerItems,
    AskUser
}

public enum CartSyncType
{
    Login,
    Logout,
    Manual,
    Automatic
}

public class CartSyncResult
{
    public bool IsValid { get; set; }
    public bool RequiresSync { get; set; }
    public List<CartItemConflict> ConflictingItems { get; set; } = new();
    public List<CartItemDto> NewItems { get; set; } = new();
    public string? ErrorMessage { get; set; }
}

public class CartItemConflict
{
    public int ProductId { get; set; }
    public int? VariantId { get; set; }
    public int GuestQuantity { get; set; }
    public int UserQuantity { get; set; }
    public string ProductName { get; set; } = string.Empty;
}

public class CartSyncEventArgs
{
    public string UserId { get; set; } = string.Empty;
    public CartSyncType SyncType { get; set; }
    public bool Success { get; set; }
    public int ItemsMerged { get; set; }
    public int ConflictsResolved { get; set; }
    public string? ErrorMessage { get; set; }
}

public class CartBackup
{
    public string UserId { get; set; } = string.Empty;
    public ShoppingCartDto Cart { get; set; } = new();
    public DateTime BackupDate { get; set; }
    public DateTime ExpiresAt { get; set; }
}
