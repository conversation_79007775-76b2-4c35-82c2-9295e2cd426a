using Microsoft.AspNetCore.SignalR;
using Microsoft.Extensions.Logging;
using NafaPlace.Notifications.Application.Interfaces;
using NafaPlace.Notifications.Application.DTOs;
using System.Collections.Concurrent;

namespace NafaPlace.Notifications.Infrastructure.Services;

public interface IRealTimeNotificationService
{
    Task SendNotificationToUserAsync(string userId, NotificationDto notification);
    Task SendNotificationToGroupAsync(string groupName, NotificationDto notification);
    Task SendBroadcastNotificationAsync(NotificationDto notification);
    Task AddUserToGroupAsync(string userId, string groupName);
    Task RemoveUserFromGroupAsync(string userId, string groupName);
    Task<int> GetOnlineUsersCountAsync();
    Task<List<string>> GetOnlineUsersAsync();
}

public class RealTimeNotificationService : IRealTimeNotificationService
{
    private readonly IHubContext<NotificationHub> _hubContext;
    private readonly ILogger<RealTimeNotificationService> _logger;
    private static readonly ConcurrentDictionary<string, string> _userConnections = new();
    private static readonly ConcurrentDictionary<string, HashSet<string>> _groupMembers = new();

    public RealTimeNotificationService(
        IHubContext<NotificationHub> hubContext,
        ILogger<RealTimeNotificationService> logger)
    {
        _hubContext = hubContext;
        _logger = logger;
    }

    public async Task SendNotificationToUserAsync(string userId, NotificationDto notification)
    {
        try
        {
            if (_userConnections.TryGetValue(userId, out var connectionId))
            {
                await _hubContext.Clients.Client(connectionId)
                    .SendAsync("ReceiveNotification", notification);
                
                _logger.LogInformation("📡 Real-time notification sent to user {UserId}: {Title}", 
                    userId, notification.Title);
            }
            else
            {
                _logger.LogDebug("User {UserId} is not connected for real-time notification", userId);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ Failed to send real-time notification to user {UserId}", userId);
        }
    }

    public async Task SendNotificationToGroupAsync(string groupName, NotificationDto notification)
    {
        try
        {
            await _hubContext.Clients.Group(groupName)
                .SendAsync("ReceiveNotification", notification);
            
            var memberCount = _groupMembers.TryGetValue(groupName, out var members) ? members.Count : 0;
            _logger.LogInformation("📡 Real-time notification sent to group {GroupName} ({MemberCount} members): {Title}", 
                groupName, memberCount, notification.Title);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ Failed to send real-time notification to group {GroupName}", groupName);
        }
    }

    public async Task SendBroadcastNotificationAsync(NotificationDto notification)
    {
        try
        {
            await _hubContext.Clients.All.SendAsync("ReceiveNotification", notification);
            
            _logger.LogInformation("📡 Broadcast notification sent to all users: {Title}", notification.Title);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ Failed to send broadcast notification");
        }
    }

    public async Task AddUserToGroupAsync(string userId, string groupName)
    {
        try
        {
            if (_userConnections.TryGetValue(userId, out var connectionId))
            {
                await _hubContext.Groups.AddToGroupAsync(connectionId, groupName);
                
                _groupMembers.AddOrUpdate(groupName, 
                    new HashSet<string> { userId },
                    (key, existing) => { existing.Add(userId); return existing; });
                
                _logger.LogDebug("User {UserId} added to group {GroupName}", userId, groupName);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ Failed to add user {UserId} to group {GroupName}", userId, groupName);
        }
    }

    public async Task RemoveUserFromGroupAsync(string userId, string groupName)
    {
        try
        {
            if (_userConnections.TryGetValue(userId, out var connectionId))
            {
                await _hubContext.Groups.RemoveFromGroupAsync(connectionId, groupName);
                
                if (_groupMembers.TryGetValue(groupName, out var members))
                {
                    members.Remove(userId);
                    if (members.Count == 0)
                    {
                        _groupMembers.TryRemove(groupName, out _);
                    }
                }
                
                _logger.LogDebug("User {UserId} removed from group {GroupName}", userId, groupName);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ Failed to remove user {UserId} from group {GroupName}", userId, groupName);
        }
    }

    public async Task<int> GetOnlineUsersCountAsync()
    {
        await Task.CompletedTask;
        return _userConnections.Count;
    }

    public async Task<List<string>> GetOnlineUsersAsync()
    {
        await Task.CompletedTask;
        return _userConnections.Keys.ToList();
    }

    // Méthodes internes pour gérer les connexions
    internal static void AddConnection(string userId, string connectionId)
    {
        _userConnections.AddOrUpdate(userId, connectionId, (key, oldValue) => connectionId);
    }

    internal static void RemoveConnection(string userId)
    {
        _userConnections.TryRemove(userId, out _);
        
        // Nettoyer les groupes
        foreach (var group in _groupMembers.ToList())
        {
            group.Value.Remove(userId);
            if (group.Value.Count == 0)
            {
                _groupMembers.TryRemove(group.Key, out _);
            }
        }
    }
}

public class NotificationHub : Hub
{
    private readonly ILogger<NotificationHub> _logger;

    public NotificationHub(ILogger<NotificationHub> logger)
    {
        _logger = logger;
    }

    public override async Task OnConnectedAsync()
    {
        var userId = GetUserId();
        if (!string.IsNullOrEmpty(userId))
        {
            RealTimeNotificationService.AddConnection(userId, Context.ConnectionId);
            
            // Ajouter automatiquement aux groupes pertinents
            await Groups.AddToGroupAsync(Context.ConnectionId, "AllUsers");
            
            // Ajouter aux groupes basés sur les rôles
            var userRoles = GetUserRoles();
            foreach (var role in userRoles)
            {
                await Groups.AddToGroupAsync(Context.ConnectionId, $"Role_{role}");
            }
            
            _logger.LogInformation("User {UserId} connected to notification hub", userId);
        }
        
        await base.OnConnectedAsync();
    }

    public override async Task OnDisconnectedAsync(Exception? exception)
    {
        var userId = GetUserId();
        if (!string.IsNullOrEmpty(userId))
        {
            RealTimeNotificationService.RemoveConnection(userId);
            _logger.LogInformation("User {UserId} disconnected from notification hub", userId);
        }
        
        await base.OnDisconnectedAsync(exception);
    }

    public async Task JoinGroup(string groupName)
    {
        var userId = GetUserId();
        if (!string.IsNullOrEmpty(userId))
        {
            await Groups.AddToGroupAsync(Context.ConnectionId, groupName);
            _logger.LogDebug("User {UserId} joined group {GroupName}", userId, groupName);
        }
    }

    public async Task LeaveGroup(string groupName)
    {
        var userId = GetUserId();
        if (!string.IsNullOrEmpty(userId))
        {
            await Groups.RemoveFromGroupAsync(Context.ConnectionId, groupName);
            _logger.LogDebug("User {UserId} left group {GroupName}", userId, groupName);
        }
    }

    public async Task MarkNotificationAsRead(int notificationId)
    {
        var userId = GetUserId();
        if (!string.IsNullOrEmpty(userId))
        {
            // Notifier les autres clients que cette notification a été lue
            await Clients.User(userId).SendAsync("NotificationMarkedAsRead", notificationId);
        }
    }

    private string GetUserId()
    {
        return Context.User?.FindFirst("sub")?.Value ?? 
               Context.User?.FindFirst("id")?.Value ?? 
               Context.User?.Identity?.Name ?? 
               string.Empty;
    }

    private List<string> GetUserRoles()
    {
        return Context.User?.FindAll("role")?.Select(c => c.Value).ToList() ?? new List<string>();
    }
}
