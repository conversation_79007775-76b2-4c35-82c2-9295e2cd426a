using Microsoft.AspNetCore.Mvc;
using NafaPlace.Catalog.Application.Common.Interfaces;
using NafaPlace.Catalog.Application.DTOs.Product;
using NafaPlace.Catalog.Infrastructure.Services;

namespace NafaPlace.Catalog.API.Controllers
{
    [ApiController]
    [Route("api/v2/products/{productId}/images")]
    public class EnhancedProductImagesController : ControllerBase
    {
        private readonly IProductImageService _productImageService;
        private readonly IImageManagementService _imageManagementService;
        private readonly IEnhancedCloudinaryService _cloudinaryService;
        private readonly ILogger<EnhancedProductImagesController> _logger;

        public EnhancedProductImagesController(
            IProductImageService productImageService,
            IImageManagementService imageManagementService,
            IEnhancedCloudinaryService cloudinaryService,
            ILogger<EnhancedProductImagesController> logger)
        {
            _productImageService = productImageService;
            _imageManagementService = imageManagementService;
            _cloudinaryService = cloudinaryService;
            _logger = logger;
        }

        /// <summary>
        /// Upload multiple images for a product
        /// </summary>
        [HttpPost("bulk-upload")]
        public async Task<ActionResult<BulkImageUploadResponse>> BulkUploadImages(
            int productId, 
            [FromBody] BulkImageUploadRequest request)
        {
            try
            {
                request.ProductId = productId;
                var result = await _imageManagementService.UploadProductImagesAsync(request);
                
                if (result.Success)
                {
                    return Ok(result);
                }
                else
                {
                    return BadRequest(result);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during bulk image upload for product {ProductId}", productId);
                return StatusCode(500, new { error = "Internal server error during image upload" });
            }
        }

        /// <summary>
        /// Manage product images (add, update, delete, reorder)
        /// </summary>
        [HttpPost("manage")]
        public async Task<ActionResult<ProductImageManagementResponse>> ManageImages(
            int productId,
            [FromBody] ProductImageManagementRequest request)
        {
            try
            {
                request.ProductId = productId;
                var result = await _imageManagementService.ManageProductImagesAsync(request);
                
                if (result.Success)
                {
                    return Ok(result);
                }
                else
                {
                    return BadRequest(result);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during image management for product {ProductId}", productId);
                return StatusCode(500, new { error = "Internal server error during image management" });
            }
        }

        /// <summary>
        /// Get all images for a product
        /// </summary>
        [HttpGet]
        public async Task<ActionResult<List<ProductImageDto>>> GetProductImages(int productId)
        {
            try
            {
                var images = await _imageManagementService.GetProductImagesAsync(productId);
                return Ok(images);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting images for product {ProductId}", productId);
                return StatusCode(500, new { error = "Internal server error" });
            }
        }

        /// <summary>
        /// Set main image for a product
        /// </summary>
        [HttpPut("{imageId}/set-main")]
        public async Task<ActionResult> SetMainImage(int productId, int imageId)
        {
            try
            {
                var success = await _imageManagementService.SetMainImageAsync(productId, imageId);
                
                if (success)
                {
                    return Ok(new { message = "Main image set successfully" });
                }
                else
                {
                    return NotFound(new { error = "Image not found or doesn't belong to this product" });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error setting main image {ImageId} for product {ProductId}", imageId, productId);
                return StatusCode(500, new { error = "Internal server error" });
            }
        }

        /// <summary>
        /// Reorder product images
        /// </summary>
        [HttpPut("reorder")]
        public async Task<ActionResult> ReorderImages(int productId, [FromBody] List<int> imageIds)
        {
            try
            {
                var success = await _imageManagementService.ReorderImagesAsync(productId, imageIds);
                
                if (success)
                {
                    return Ok(new { message = "Images reordered successfully" });
                }
                else
                {
                    return BadRequest(new { error = "Failed to reorder images" });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error reordering images for product {ProductId}", productId);
                return StatusCode(500, new { error = "Internal server error" });
            }
        }

        /// <summary>
        /// Delete a product image
        /// </summary>
        [HttpDelete("{imageId}")]
        public async Task<ActionResult> DeleteImage(int productId, int imageId)
        {
            try
            {
                var success = await _imageManagementService.DeleteProductImageAsync(imageId);
                
                if (success)
                {
                    return Ok(new { message = "Image deleted successfully" });
                }
                else
                {
                    return NotFound(new { error = "Image not found" });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting image {ImageId}", imageId);
                return StatusCode(500, new { error = "Internal server error" });
            }
        }

        /// <summary>
        /// Validate an image before upload
        /// </summary>
        [HttpPost("validate")]
        public async Task<ActionResult<ImageValidationResult>> ValidateImage([FromBody] ValidateImageRequest request)
        {
            try
            {
                using var imageStream = new MemoryStream(Convert.FromBase64String(request.Base64Image));
                var result = await _cloudinaryService.ValidateImageAsync(imageStream, request.FileName);
                
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating image {FileName}", request.FileName);
                return Ok(new ImageValidationResult 
                { 
                    IsValid = false, 
                    ErrorMessage = $"Validation error: {ex.Message}" 
                });
            }
        }

        /// <summary>
        /// Generate optimized URLs for an image
        /// </summary>
        [HttpPost("{imageId}/variants")]
        public async Task<ActionResult<List<string>>> GenerateImageVariants(
            int productId, 
            int imageId, 
            [FromBody] List<ImageVariant> variants)
        {
            try
            {
                // Récupérer l'image pour obtenir le publicId
                var images = await _imageManagementService.GetProductImagesAsync(productId);
                var image = images.FirstOrDefault(i => i.Id == imageId);
                
                if (image?.PublicId == null)
                {
                    return NotFound(new { error = "Image not found or missing publicId" });
                }

                var urls = await _imageManagementService.GenerateImageVariantsAsync(image.PublicId, variants);
                return Ok(urls);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating variants for image {ImageId}", imageId);
                return StatusCode(500, new { error = "Internal server error" });
            }
        }

        /// <summary>
        /// Get image statistics
        /// </summary>
        [HttpGet("statistics")]
        public async Task<ActionResult<ImageStatistics>> GetImageStatistics()
        {
            try
            {
                var stats = await _imageManagementService.GetImageStatisticsAsync();
                return Ok(stats);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting image statistics");
                return StatusCode(500, new { error = "Internal server error" });
            }
        }

        /// <summary>
        /// Get optimized image URL with transformations
        /// </summary>
        [HttpGet("{imageId}/optimized")]
        public async Task<ActionResult<string>> GetOptimizedImageUrl(
            int productId, 
            int imageId,
            [FromQuery] int? width = null,
            [FromQuery] int? height = null,
            [FromQuery] int? quality = null,
            [FromQuery] string? crop = null)
        {
            try
            {
                var images = await _imageManagementService.GetProductImagesAsync(productId);
                var image = images.FirstOrDefault(i => i.Id == imageId);
                
                if (image?.PublicId == null)
                {
                    return NotFound(new { error = "Image not found or missing publicId" });
                }

                var transformation = new ImageTransformation
                {
                    Width = width,
                    Height = height,
                    Quality = quality,
                    Crop = crop ?? "fill"
                };

                var optimizedUrl = await _cloudinaryService.GenerateOptimizedUrlAsync(image.PublicId, transformation);
                return Ok(new { url = optimizedUrl });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating optimized URL for image {ImageId}", imageId);
                return StatusCode(500, new { error = "Internal server error" });
            }
        }
    }

    // DTOs pour les requêtes
    public class ValidateImageRequest
    {
        public string Base64Image { get; set; } = string.Empty;
        public string FileName { get; set; } = string.Empty;
    }
}
