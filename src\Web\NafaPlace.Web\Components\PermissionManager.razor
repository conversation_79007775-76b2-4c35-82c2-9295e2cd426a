@using System.Text.Json
@inject HttpClient HttpClient
@inject IJSRuntime JSRuntime

<div class="permission-manager">
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="fas fa-shield-alt me-2"></i>
                Gestion des Permissions
            </h5>
        </div>
        <div class="card-body">
            @if (_isLoading)
            {
                <div class="text-center py-4">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Chargement...</span>
                    </div>
                    <p class="mt-2">Chargement des permissions...</p>
                </div>
            }
            else
            {
                <div class="row">
                    <!-- Sélection du type -->
                    <div class="col-md-3">
                        <div class="nav flex-column nav-pills" role="tablist">
                            <button class="nav-link @(_selectedTab == "users" ? "active" : "")" 
                                    @onclick="() => SelectTab('users')">
                                <i class="fas fa-users me-2"></i>
                                Utilisateurs
                            </button>
                            <button class="nav-link @(_selectedTab == "roles" ? "active" : "")" 
                                    @onclick="() => SelectTab('roles')">
                                <i class="fas fa-user-tag me-2"></i>
                                Rôles
                            </button>
                            <button class="nav-link @(_selectedTab == "permissions" ? "active" : "")" 
                                    @onclick="() => SelectTab('permissions')">
                                <i class="fas fa-key me-2"></i>
                                Permissions
                            </button>
                        </div>
                    </div>

                    <!-- Contenu principal -->
                    <div class="col-md-9">
                        @if (_selectedTab == "users")
                        {
                            <div class="user-permissions">
                                <h6>Permissions des Utilisateurs</h6>
                                
                                <div class="mb-3">
                                    <label class="form-label">Sélectionner un utilisateur :</label>
                                    <select class="form-select" @bind="_selectedUserId" @bind:after="LoadUserPermissions">
                                        <option value="">-- Choisir un utilisateur --</option>
                                        @foreach (var user in _users)
                                        {
                                            <option value="@user.Id">@user.Name (@user.Email)</option>
                                        }
                                    </select>
                                </div>

                                @if (!string.IsNullOrEmpty(_selectedUserId))
                                {
                                    <div class="permissions-grid">
                                        @foreach (var category in _permissionsByCategory.Keys)
                                        {
                                            <div class="permission-category mb-4">
                                                <h6 class="category-title">
                                                    <i class="fas fa-folder me-2"></i>
                                                    @GetCategoryDisplayName(category)
                                                </h6>
                                                <div class="permission-items">
                                                    @foreach (var permission in _permissionsByCategory[category])
                                                    {
                                                        <div class="form-check">
                                                            <input class="form-check-input" 
                                                                   type="checkbox" 
                                                                   id="<EMAIL>"
                                                                   checked="@_userPermissions.Contains(permission.Code)"
                                                                   @onchange="(e) => ToggleUserPermission(permission.Code, (bool)e.Value!)">
                                                            <label class="form-check-label" for="<EMAIL>">
                                                                @permission.Name
                                                                <small class="text-muted d-block">@permission.Description</small>
                                                            </label>
                                                        </div>
                                                    }
                                                </div>
                                            </div>
                                        }
                                    </div>
                                }
                            </div>
                        }
                        else if (_selectedTab == "roles")
                        {
                            <div class="role-permissions">
                                <h6>Permissions des Rôles</h6>
                                
                                <div class="mb-3">
                                    <label class="form-label">Sélectionner un rôle :</label>
                                    <select class="form-select" @bind="_selectedRoleId" @bind:after="LoadRolePermissions">
                                        <option value="">-- Choisir un rôle --</option>
                                        @foreach (var role in _roles)
                                        {
                                            <option value="@role.Id">@role.Name</option>
                                        }
                                    </select>
                                </div>

                                @if (!string.IsNullOrEmpty(_selectedRoleId))
                                {
                                    <div class="permissions-grid">
                                        @foreach (var category in _permissionsByCategory.Keys)
                                        {
                                            <div class="permission-category mb-4">
                                                <h6 class="category-title">
                                                    <i class="fas fa-folder me-2"></i>
                                                    @GetCategoryDisplayName(category)
                                                </h6>
                                                <div class="permission-items">
                                                    @foreach (var permission in _permissionsByCategory[category])
                                                    {
                                                        <div class="form-check">
                                                            <input class="form-check-input" 
                                                                   type="checkbox" 
                                                                   id="<EMAIL>"
                                                                   checked="@_rolePermissions.Contains(permission.Code)"
                                                                   @onchange="(e) => ToggleRolePermission(permission.Code, (bool)e.Value!)">
                                                            <label class="form-check-label" for="<EMAIL>">
                                                                @permission.Name
                                                                <small class="text-muted d-block">@permission.Description</small>
                                                            </label>
                                                        </div>
                                                    }
                                                </div>
                                            </div>
                                        }
                                    </div>
                                }
                            </div>
                        }
                        else if (_selectedTab == "permissions")
                        {
                            <div class="all-permissions">
                                <h6>Toutes les Permissions</h6>
                                
                                <div class="permissions-list">
                                    @foreach (var category in _permissionsByCategory.Keys)
                                    {
                                        <div class="permission-category mb-4">
                                            <h6 class="category-title">
                                                <i class="fas fa-folder me-2"></i>
                                                @GetCategoryDisplayName(category)
                                                <span class="badge bg-secondary ms-2">@_permissionsByCategory[category].Count</span>
                                            </h6>
                                            <div class="table-responsive">
                                                <table class="table table-sm">
                                                    <thead>
                                                        <tr>
                                                            <th>Nom</th>
                                                            <th>Code</th>
                                                            <th>Description</th>
                                                            <th>Ressource</th>
                                                            <th>Action</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        @foreach (var permission in _permissionsByCategory[category])
                                                        {
                                                            <tr>
                                                                <td>@permission.Name</td>
                                                                <td><code>@permission.Code</code></td>
                                                                <td>@permission.Description</td>
                                                                <td>@permission.Resource</td>
                                                                <td>@permission.Action</td>
                                                            </tr>
                                                        }
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                    }
                                </div>
                            </div>
                        }
                    </div>
                </div>

                @if (_selectedTab != "permissions")
                {
                    <div class="card-footer">
                        <button class="btn btn-primary" @onclick="SaveChanges" disabled="@_isSaving">
                            @if (_isSaving)
                            {
                                <span class="spinner-border spinner-border-sm me-2"></span>
                            }
                            <i class="fas fa-save me-2"></i>
                            Enregistrer les modifications
                        </button>
                        
                        <button class="btn btn-outline-secondary ms-2" @onclick="ResetChanges">
                            <i class="fas fa-undo me-2"></i>
                            Annuler
                        </button>
                    </div>
                }
            }
        </div>
    </div>
</div>

@code {
    [Parameter] public EventCallback<string> OnPermissionChanged { get; set; }

    private bool _isLoading = true;
    private bool _isSaving = false;
    private string _selectedTab = "users";
    private string _selectedUserId = "";
    private string _selectedRoleId = "";

    private List<UserDto> _users = new();
    private List<RoleDto> _roles = new();
    private Dictionary<string, List<PermissionDto>> _permissionsByCategory = new();
    private HashSet<string> _userPermissions = new();
    private HashSet<string> _rolePermissions = new();
    private HashSet<string> _originalUserPermissions = new();
    private HashSet<string> _originalRolePermissions = new();

    protected override async Task OnInitializedAsync()
    {
        await LoadData();
    }

    private async Task LoadData()
    {
        _isLoading = true;
        try
        {
            // Charger les utilisateurs, rôles et permissions
            var usersTask = LoadUsers();
            var rolesTask = LoadRoles();
            var permissionsTask = LoadPermissions();

            await Task.WhenAll(usersTask, rolesTask, permissionsTask);
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("showToast", "Erreur lors du chargement des données", "error");
            Console.WriteLine($"Erreur: {ex.Message}");
        }
        finally
        {
            _isLoading = false;
        }
    }

    private async Task LoadUsers()
    {
        try
        {
            var response = await HttpClient.GetAsync("/api/users");
            if (response.IsSuccessStatusCode)
            {
                var json = await response.Content.ReadAsStringAsync();
                _users = JsonSerializer.Deserialize<List<UserDto>>(json, new JsonSerializerOptions { PropertyNameCaseInsensitive = true }) ?? new();
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur chargement utilisateurs: {ex.Message}");
        }
    }

    private async Task LoadRoles()
    {
        try
        {
            var response = await HttpClient.GetAsync("/api/roles");
            if (response.IsSuccessStatusCode)
            {
                var json = await response.Content.ReadAsStringAsync();
                _roles = JsonSerializer.Deserialize<List<RoleDto>>(json, new JsonSerializerOptions { PropertyNameCaseInsensitive = true }) ?? new();
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur chargement rôles: {ex.Message}");
        }
    }

    private async Task LoadPermissions()
    {
        try
        {
            var response = await HttpClient.GetAsync("/api/permissions");
            if (response.IsSuccessStatusCode)
            {
                var json = await response.Content.ReadAsStringAsync();
                var permissions = JsonSerializer.Deserialize<List<PermissionDto>>(json, new JsonSerializerOptions { PropertyNameCaseInsensitive = true }) ?? new();
                
                _permissionsByCategory = permissions
                    .GroupBy(p => p.Category)
                    .ToDictionary(g => g.Key, g => g.OrderBy(p => p.Name).ToList());
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur chargement permissions: {ex.Message}");
        }
    }

    private async Task LoadUserPermissions()
    {
        if (string.IsNullOrEmpty(_selectedUserId)) return;

        try
        {
            var response = await HttpClient.GetAsync($"/api/permissions/user/{_selectedUserId}");
            if (response.IsSuccessStatusCode)
            {
                var json = await response.Content.ReadAsStringAsync();
                var permissions = JsonSerializer.Deserialize<List<PermissionDto>>(json, new JsonSerializerOptions { PropertyNameCaseInsensitive = true }) ?? new();
                
                _userPermissions = permissions.Select(p => p.Code).ToHashSet();
                _originalUserPermissions = new HashSet<string>(_userPermissions);
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur chargement permissions utilisateur: {ex.Message}");
        }
    }

    private async Task LoadRolePermissions()
    {
        if (string.IsNullOrEmpty(_selectedRoleId)) return;

        try
        {
            var response = await HttpClient.GetAsync($"/api/permissions/role/{_selectedRoleId}");
            if (response.IsSuccessStatusCode)
            {
                var json = await response.Content.ReadAsStringAsync();
                var permissions = JsonSerializer.Deserialize<List<PermissionDto>>(json, new JsonSerializerOptions { PropertyNameCaseInsensitive = true }) ?? new();
                
                _rolePermissions = permissions.Select(p => p.Code).ToHashSet();
                _originalRolePermissions = new HashSet<string>(_rolePermissions);
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur chargement permissions rôle: {ex.Message}");
        }
    }

    private void SelectTab(string tab)
    {
        _selectedTab = tab;
        _selectedUserId = "";
        _selectedRoleId = "";
        _userPermissions.Clear();
        _rolePermissions.Clear();
    }

    private void ToggleUserPermission(string permissionCode, bool isChecked)
    {
        if (isChecked)
            _userPermissions.Add(permissionCode);
        else
            _userPermissions.Remove(permissionCode);
    }

    private void ToggleRolePermission(string permissionCode, bool isChecked)
    {
        if (isChecked)
            _rolePermissions.Add(permissionCode);
        else
            _rolePermissions.Remove(permissionCode);
    }

    private async Task SaveChanges()
    {
        _isSaving = true;
        try
        {
            if (_selectedTab == "users" && !string.IsNullOrEmpty(_selectedUserId))
            {
                await SaveUserPermissions();
            }
            else if (_selectedTab == "roles" && !string.IsNullOrEmpty(_selectedRoleId))
            {
                await SaveRolePermissions();
            }

            await JSRuntime.InvokeVoidAsync("showToast", "Permissions mises à jour avec succès", "success");
            await OnPermissionChanged.InvokeAsync(_selectedTab);
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("showToast", "Erreur lors de la sauvegarde", "error");
            Console.WriteLine($"Erreur sauvegarde: {ex.Message}");
        }
        finally
        {
            _isSaving = false;
        }
    }

    private async Task SaveUserPermissions()
    {
        // Simuler la sauvegarde des permissions utilisateur
        // Dans une vraie application, on ferait un appel API
        await Task.Delay(500);
        _originalUserPermissions = new HashSet<string>(_userPermissions);
    }

    private async Task SaveRolePermissions()
    {
        // Simuler la sauvegarde des permissions de rôle
        // Dans une vraie application, on ferait un appel API
        await Task.Delay(500);
        _originalRolePermissions = new HashSet<string>(_rolePermissions);
    }

    private void ResetChanges()
    {
        if (_selectedTab == "users")
        {
            _userPermissions = new HashSet<string>(_originalUserPermissions);
        }
        else if (_selectedTab == "roles")
        {
            _rolePermissions = new HashSet<string>(_originalRolePermissions);
        }
    }

    private string GetCategoryDisplayName(string category)
    {
        return category switch
        {
            "UserManagement" => "Gestion des Utilisateurs",
            "ProductManagement" => "Gestion des Produits",
            "OrderManagement" => "Gestion des Commandes",
            "InventoryManagement" => "Gestion des Stocks",
            "FinancialManagement" => "Gestion Financière",
            "ContentManagement" => "Gestion de Contenu",
            "SystemAdministration" => "Administration Système",
            "Analytics" => "Analyses et Rapports",
            "Marketing" => "Marketing",
            "CustomerService" => "Service Client",
            _ => category
        };
    }

    // DTOs
    public class UserDto
    {
        public int Id { get; set; }
        public string Name { get; set; } = "";
        public string Email { get; set; } = "";
    }

    public class RoleDto
    {
        public int Id { get; set; }
        public string Name { get; set; } = "";
        public string Description { get; set; } = "";
    }

    public class PermissionDto
    {
        public int Id { get; set; }
        public string Name { get; set; } = "";
        public string Code { get; set; } = "";
        public string Description { get; set; } = "";
        public string Category { get; set; } = "";
        public string Resource { get; set; } = "";
        public string Action { get; set; } = "";
    }
}

<style>
    .permission-manager .nav-pills .nav-link {
        text-align: left;
        margin-bottom: 0.5rem;
    }

    .permission-category {
        border: 1px solid #dee2e6;
        border-radius: 0.375rem;
        padding: 1rem;
        background-color: #f8f9fa;
    }

    .category-title {
        color: #495057;
        border-bottom: 1px solid #dee2e6;
        padding-bottom: 0.5rem;
        margin-bottom: 1rem;
    }

    .permission-items .form-check {
        margin-bottom: 0.75rem;
    }

    .permission-items .form-check-label {
        cursor: pointer;
    }

    .permissions-grid {
        max-height: 600px;
        overflow-y: auto;
    }

    .table th {
        background-color: #f8f9fa;
        border-top: none;
    }

    .table code {
        background-color: #e9ecef;
        padding: 0.25rem 0.5rem;
        border-radius: 0.25rem;
        font-size: 0.875rem;
    }
</style>
