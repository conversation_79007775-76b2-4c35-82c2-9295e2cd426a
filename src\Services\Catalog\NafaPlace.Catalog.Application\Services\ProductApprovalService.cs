using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using NafaPlace.Catalog.Infrastructure.Data;
using NafaPlace.Catalog.Domain.Models;
using NafaPlace.Catalog.Domain.Enums;
using NafaPlace.Catalog.Application.DTOs.Product;
using AutoMapper;

namespace NafaPlace.Catalog.Application.Services;

public interface IProductApprovalService
{
    Task<ProductApprovalResult> SubmitForApprovalAsync(int productId, string submittedBy);
    Task<ProductApprovalResult> ApproveProductAsync(int productId, string approvedBy, string? notes = null);
    Task<ProductApprovalResult> RejectProductAsync(int productId, string rejectedBy, string rejectionReason, string? notes = null);
    Task<ProductApprovalResult> RequestChangesAsync(int productId, string requestedBy, string changeRequests);
    Task<List<ProductApprovalDto>> GetPendingApprovalsAsync(int page = 1, int pageSize = 20);
    Task<List<ProductApprovalDto>> GetApprovalHistoryAsync(int productId);
    Task<ProductApprovalStatsDto> GetApprovalStatsAsync(DateTime? startDate = null, DateTime? endDate = null);
    Task<List<ProductApprovalDto>> GetApprovalsByStatusAsync(ProductApprovalStatus status, int page = 1, int pageSize = 20);
    Task<bool> CanUserApproveProductAsync(string userId, int productId);
    Task<ProductApprovalWorkflowDto> GetApprovalWorkflowAsync(int productId);
    Task<bool> BulkApproveProductsAsync(List<int> productIds, string approvedBy, string? notes = null);
    Task<bool> BulkRejectProductsAsync(List<int> productIds, string rejectedBy, string rejectionReason);
}

public class ProductApprovalService : IProductApprovalService
{
    private readonly CatalogDbContext _context;
    private readonly IMapper _mapper;
    private readonly ILogger<ProductApprovalService> _logger;
    private readonly IProductApprovalNotificationService _notificationService;
    private readonly IProductApprovalAuditService _auditService;

    public ProductApprovalService(
        CatalogDbContext context,
        IMapper mapper,
        ILogger<ProductApprovalService> logger,
        IProductApprovalNotificationService notificationService,
        IProductApprovalAuditService auditService)
    {
        _context = context;
        _mapper = mapper;
        _logger = logger;
        _notificationService = notificationService;
        _auditService = auditService;
    }

    public async Task<ProductApprovalResult> SubmitForApprovalAsync(int productId, string submittedBy)
    {
        try
        {
            var product = await _context.Products
                .Include(p => p.Seller)
                .FirstOrDefaultAsync(p => p.Id == productId);

            if (product == null)
            {
                return new ProductApprovalResult
                {
                    Success = false,
                    ErrorMessage = "Produit introuvable"
                };
            }

            // Vérifier que le produit peut être soumis pour approbation
            if (product.ApprovalStatus != ProductApprovalStatus.Pending && 
                product.ApprovalStatus != ProductApprovalStatus.Rejected)
            {
                return new ProductApprovalResult
                {
                    Success = false,
                    ErrorMessage = "Le produit ne peut pas être soumis pour approbation dans son état actuel"
                };
            }

            // Valider que le produit est complet
            var validationResult = await ValidateProductForApprovalAsync(product);
            if (!validationResult.IsValid)
            {
                return new ProductApprovalResult
                {
                    Success = false,
                    ErrorMessage = "Le produit ne respecte pas les critères d'approbation",
                    ValidationErrors = validationResult.Errors
                };
            }

            // Mettre à jour le statut
            product.ApprovalStatus = ProductApprovalStatus.Pending;
            product.UpdatedAt = DateTime.UtcNow;

            // Créer un enregistrement d'audit
            await _auditService.LogApprovalActionAsync(productId, ProductApprovalAction.SubmittedForApproval, submittedBy);

            await _context.SaveChangesAsync();

            // Envoyer des notifications
            await _notificationService.NotifyProductSubmittedForApprovalAsync(productId, product.SellerId.ToString(), submittedBy);

            _logger.LogInformation("Product {ProductId} submitted for approval by {SubmittedBy}", productId, submittedBy);

            return new ProductApprovalResult
            {
                Success = true,
                ProductId = productId,
                NewStatus = ProductApprovalStatus.Pending,
                Message = "Produit soumis pour approbation avec succès"
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error submitting product {ProductId} for approval", productId);
            return new ProductApprovalResult
            {
                Success = false,
                ErrorMessage = "Erreur lors de la soumission pour approbation"
            };
        }
    }

    public async Task<ProductApprovalResult> ApproveProductAsync(int productId, string approvedBy, string? notes = null)
    {
        try
        {
            var product = await _context.Products
                .Include(p => p.Seller)
                .FirstOrDefaultAsync(p => p.Id == productId);

            if (product == null)
            {
                return new ProductApprovalResult
                {
                    Success = false,
                    ErrorMessage = "Produit introuvable"
                };
            }

            if (product.ApprovalStatus != ProductApprovalStatus.Pending)
            {
                return new ProductApprovalResult
                {
                    Success = false,
                    ErrorMessage = "Seuls les produits en attente peuvent être approuvés"
                };
            }

            // Mettre à jour le statut d'approbation
            product.ApprovalStatus = ProductApprovalStatus.Approved;
            product.ApprovedAt = DateTime.UtcNow;
            product.ApprovedBy = approvedBy;
            product.RejectionReason = null;
            product.UpdatedAt = DateTime.UtcNow;
            product.IsActive = true; // Activer automatiquement le produit approuvé

            // Créer un enregistrement d'audit
            await _auditService.LogApprovalActionAsync(productId, ProductApprovalAction.Approved, approvedBy, notes);

            await _context.SaveChangesAsync();

            // Envoyer des notifications
            await _notificationService.NotifyProductApprovedAsync(productId, product.SellerId.ToString(), approvedBy);

            _logger.LogInformation("Product {ProductId} approved by {ApprovedBy}", productId, approvedBy);

            return new ProductApprovalResult
            {
                Success = true,
                ProductId = productId,
                NewStatus = ProductApprovalStatus.Approved,
                Message = "Produit approuvé avec succès"
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error approving product {ProductId}", productId);
            return new ProductApprovalResult
            {
                Success = false,
                ErrorMessage = "Erreur lors de l'approbation du produit"
            };
        }
    }

    public async Task<ProductApprovalResult> RejectProductAsync(int productId, string rejectedBy, string rejectionReason, string? notes = null)
    {
        try
        {
            var product = await _context.Products
                .Include(p => p.Seller)
                .FirstOrDefaultAsync(p => p.Id == productId);

            if (product == null)
            {
                return new ProductApprovalResult
                {
                    Success = false,
                    ErrorMessage = "Produit introuvable"
                };
            }

            if (product.ApprovalStatus != ProductApprovalStatus.Pending)
            {
                return new ProductApprovalResult
                {
                    Success = false,
                    ErrorMessage = "Seuls les produits en attente peuvent être rejetés"
                };
            }

            // Mettre à jour le statut de rejet
            product.ApprovalStatus = ProductApprovalStatus.Rejected;
            product.RejectionReason = rejectionReason;
            product.ApprovedAt = null;
            product.ApprovedBy = rejectedBy;
            product.UpdatedAt = DateTime.UtcNow;
            product.IsActive = false; // Désactiver le produit rejeté

            // Créer un enregistrement d'audit
            await _auditService.LogApprovalActionAsync(productId, ProductApprovalAction.Rejected, rejectedBy, notes, rejectionReason);

            await _context.SaveChangesAsync();

            // Envoyer des notifications
            await _notificationService.NotifyProductRejectedAsync(productId, product.SellerId.ToString(), rejectedBy, rejectionReason);

            _logger.LogInformation("Product {ProductId} rejected by {RejectedBy} with reason: {Reason}", productId, rejectedBy, rejectionReason);

            return new ProductApprovalResult
            {
                Success = true,
                ProductId = productId,
                NewStatus = ProductApprovalStatus.Rejected,
                Message = "Produit rejeté avec succès"
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error rejecting product {ProductId}", productId);
            return new ProductApprovalResult
            {
                Success = false,
                ErrorMessage = "Erreur lors du rejet du produit"
            };
        }
    }

    public async Task<List<ProductApprovalDto>> GetPendingApprovalsAsync(int page = 1, int pageSize = 20)
    {
        try
        {
            var products = await _context.Products
                .Include(p => p.Seller)
                .Include(p => p.Images)
                .Where(p => p.ApprovalStatus == ProductApprovalStatus.Pending)
                .OrderBy(p => p.CreatedAt)
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();

            return products.Select(p => new ProductApprovalDto
            {
                ProductId = p.Id,
                ProductName = p.Name,
                SellerName = p.Seller?.Name ?? "Vendeur inconnu",
                SellerId = p.SellerId,
                Status = p.ApprovalStatus,
                SubmittedAt = p.CreatedAt,
                ImageUrl = p.Images.FirstOrDefault()?.ImageUrl,
                Price = p.Price,
                Category = p.Category?.Name ?? "Non catégorisé"
            }).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting pending approvals");
            return new List<ProductApprovalDto>();
        }
    }

    public async Task<ProductApprovalStatsDto> GetApprovalStatsAsync(DateTime? startDate = null, DateTime? endDate = null)
    {
        try
        {
            startDate ??= DateTime.UtcNow.AddDays(-30);
            endDate ??= DateTime.UtcNow;

            var products = await _context.Products
                .Where(p => p.CreatedAt >= startDate && p.CreatedAt <= endDate)
                .ToListAsync();

            return new ProductApprovalStatsDto
            {
                TotalProducts = products.Count,
                PendingApprovals = products.Count(p => p.ApprovalStatus == ProductApprovalStatus.Pending),
                ApprovedProducts = products.Count(p => p.ApprovalStatus == ProductApprovalStatus.Approved),
                RejectedProducts = products.Count(p => p.ApprovalStatus == ProductApprovalStatus.Rejected),
                AverageApprovalTimeHours = await CalculateAverageApprovalTimeAsync(startDate.Value, endDate.Value),
                ApprovalRate = products.Count > 0 ? (double)products.Count(p => p.ApprovalStatus == ProductApprovalStatus.Approved) / products.Count * 100 : 0
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting approval stats");
            return new ProductApprovalStatsDto();
        }
    }

    // Méthodes privées
    private async Task<ProductValidationResult> ValidateProductForApprovalAsync(Product product)
    {
        var result = new ProductValidationResult { IsValid = true };

        // Vérifications de base
        if (string.IsNullOrWhiteSpace(product.Name))
            result.Errors.Add("Le nom du produit est requis");

        if (string.IsNullOrWhiteSpace(product.Description))
            result.Errors.Add("La description du produit est requise");

        if (product.Price <= 0)
            result.Errors.Add("Le prix doit être supérieur à zéro");

        if (product.CategoryId <= 0)
            result.Errors.Add("Une catégorie doit être sélectionnée");

        // Vérifier les images
        var images = await _context.ProductImages.Where(i => i.ProductId == product.Id).ToListAsync();
        if (!images.Any())
            result.Errors.Add("Au moins une image est requise");

        result.IsValid = !result.Errors.Any();
        return result;
    }

    private async Task<double> CalculateAverageApprovalTimeAsync(DateTime startDate, DateTime endDate)
    {
        var approvedProducts = await _context.Products
            .Where(p => p.ApprovalStatus == ProductApprovalStatus.Approved &&
                       p.ApprovedAt.HasValue &&
                       p.CreatedAt >= startDate &&
                       p.CreatedAt <= endDate)
            .ToListAsync();

        if (!approvedProducts.Any())
            return 0;

        var totalHours = approvedProducts
            .Where(p => p.ApprovedAt.HasValue)
            .Sum(p => (p.ApprovedAt!.Value - p.CreatedAt).TotalHours);

        return totalHours / approvedProducts.Count;
    }

    // Méthodes non implémentées (à compléter selon les besoins)
    public Task<ProductApprovalResult> RequestChangesAsync(int productId, string requestedBy, string changeRequests) => throw new NotImplementedException();
    public Task<List<ProductApprovalDto>> GetApprovalHistoryAsync(int productId) => throw new NotImplementedException();
    public Task<List<ProductApprovalDto>> GetApprovalsByStatusAsync(ProductApprovalStatus status, int page = 1, int pageSize = 20) => throw new NotImplementedException();
    public Task<bool> CanUserApproveProductAsync(string userId, int productId) => throw new NotImplementedException();
    public Task<ProductApprovalWorkflowDto> GetApprovalWorkflowAsync(int productId) => throw new NotImplementedException();
    public Task<bool> BulkApproveProductsAsync(List<int> productIds, string approvedBy, string? notes = null) => throw new NotImplementedException();
    public Task<bool> BulkRejectProductsAsync(List<int> productIds, string rejectedBy, string rejectionReason) => throw new NotImplementedException();
}
