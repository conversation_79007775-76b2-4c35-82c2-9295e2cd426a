@using Microsoft.AspNetCore.SignalR.Client
@using System.Text.Json
@implements IAsyncDisposable
@inject IJSRuntime JSRuntime
@inject NavigationManager Navigation
@inject AuthenticationStateProvider AuthStateProvider

<div class="notification-center">
    <!-- Notification Bell Icon -->
    <div class="notification-bell" @onclick="ToggleNotifications">
        <i class="fas fa-bell"></i>
        @if (_unreadCount > 0)
        {
            <span class="notification-badge">@(_unreadCount > 99 ? "99+" : _unreadCount.ToString())</span>
        }
    </div>

    <!-- Notification Dropdown -->
    @if (_showNotifications)
    {
        <div class="notification-dropdown">
            <div class="notification-header">
                <h6 class="mb-0">Notifications</h6>
                @if (_unreadCount > 0)
                {
                    <button class="btn btn-sm btn-link" @onclick="MarkAllAsRead">
                        Tout marquer comme lu
                    </button>
                }
            </div>

            <div class="notification-list">
                @if (_notifications.Any())
                {
                    @foreach (var notification in _notifications.Take(10))
                    {
                        <div class="notification-item @(notification.IsRead ? "read" : "unread")" 
                             @onclick="() => HandleNotificationClick(notification)">
                            <div class="notification-icon">
                                <i class="@GetNotificationIcon(notification.Type)"></i>
                            </div>
                            <div class="notification-content">
                                <div class="notification-title">@notification.Title</div>
                                <div class="notification-message">@notification.Message</div>
                                <div class="notification-time">@GetTimeAgo(notification.CreatedAt)</div>
                            </div>
                            @if (!notification.IsRead)
                            {
                                <div class="unread-indicator"></div>
                            }
                        </div>
                    }
                }
                else
                {
                    <div class="no-notifications">
                        <i class="fas fa-bell-slash"></i>
                        <p>Aucune notification</p>
                    </div>
                }
            </div>

            @if (_notifications.Count > 10)
            {
                <div class="notification-footer">
                    <a href="/notifications" class="btn btn-sm btn-primary w-100">
                        Voir toutes les notifications
                    </a>
                </div>
            }
        </div>
    }
</div>

@code {
    private HubConnection? _hubConnection;
    private List<NotificationDto> _notifications = new();
    private int _unreadCount = 0;
    private bool _showNotifications = false;
    private string _userId = string.Empty;

    protected override async Task OnInitializedAsync()
    {
        var authState = await AuthStateProvider.GetAuthenticationStateAsync();
        if (authState.User.Identity?.IsAuthenticated == true)
        {
            _userId = authState.User.FindFirst("sub")?.Value ?? 
                     authState.User.FindFirst("id")?.Value ?? 
                     authState.User.Identity.Name ?? string.Empty;

            if (!string.IsNullOrEmpty(_userId))
            {
                await InitializeSignalR();
                await LoadNotifications();
            }
        }
    }

    private async Task InitializeSignalR()
    {
        try
        {
            _hubConnection = new HubConnectionBuilder()
                .WithUrl(Navigation.ToAbsoluteUri("/notificationHub"))
                .Build();

            _hubConnection.On<NotificationDto>("ReceiveNotification", async (notification) =>
            {
                _notifications.Insert(0, notification);
                if (!notification.IsRead)
                {
                    _unreadCount++;
                }
                
                await InvokeAsync(StateHasChanged);
                await ShowToastNotification(notification);
            });

            _hubConnection.On<int>("NotificationMarkedAsRead", (notificationId) =>
            {
                var notification = _notifications.FirstOrDefault(n => n.Id == notificationId);
                if (notification != null && !notification.IsRead)
                {
                    notification.IsRead = true;
                    _unreadCount = Math.Max(0, _unreadCount - 1);
                    InvokeAsync(StateHasChanged);
                }
            });

            await _hubConnection.StartAsync();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur SignalR: {ex.Message}");
        }
    }

    private async Task LoadNotifications()
    {
        try
        {
            // Simuler le chargement des notifications
            // Dans une vraie application, ceci ferait appel à l'API
            await Task.Delay(100);
            
            // Exemple de notifications
            _notifications = new List<NotificationDto>
            {
                new() { Id = 1, Title = "Commande confirmée", Message = "Votre commande #12345 a été confirmée", Type = "order", CreatedAt = DateTime.Now.AddMinutes(-5), IsRead = false },
                new() { Id = 2, Title = "Nouveau produit", Message = "Un nouveau produit correspond à vos préférences", Type = "product", CreatedAt = DateTime.Now.AddHours(-2), IsRead = true },
                new() { Id = 3, Title = "Promotion", Message = "Profitez de 20% de réduction sur votre prochain achat", Type = "promotion", CreatedAt = DateTime.Now.AddDays(-1), IsRead = false }
            };

            _unreadCount = _notifications.Count(n => !n.IsRead);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur chargement notifications: {ex.Message}");
        }
    }

    private void ToggleNotifications()
    {
        _showNotifications = !_showNotifications;
    }

    private async Task HandleNotificationClick(NotificationDto notification)
    {
        if (!notification.IsRead)
        {
            notification.IsRead = true;
            _unreadCount = Math.Max(0, _unreadCount - 1);
            
            // Marquer comme lu via SignalR
            if (_hubConnection?.State == HubConnectionState.Connected)
            {
                await _hubConnection.SendAsync("MarkNotificationAsRead", notification.Id);
            }
        }

        // Naviguer vers l'action si définie
        if (!string.IsNullOrEmpty(notification.ActionUrl))
        {
            Navigation.NavigateTo(notification.ActionUrl);
        }

        _showNotifications = false;
    }

    private async Task MarkAllAsRead()
    {
        foreach (var notification in _notifications.Where(n => !n.IsRead))
        {
            notification.IsRead = true;
        }
        _unreadCount = 0;
        
        // Notifier via SignalR
        if (_hubConnection?.State == HubConnectionState.Connected)
        {
            await _hubConnection.SendAsync("MarkAllNotificationsAsRead");
        }
    }

    private async Task ShowToastNotification(NotificationDto notification)
    {
        try
        {
            await JSRuntime.InvokeVoidAsync("showToast", notification.Title, "info", notification.Message);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur toast: {ex.Message}");
        }
    }

    private string GetNotificationIcon(string type) => type switch
    {
        "order" => "fas fa-shopping-cart text-success",
        "payment" => "fas fa-credit-card text-info",
        "product" => "fas fa-box text-primary",
        "review" => "fas fa-star text-warning",
        "promotion" => "fas fa-tag text-danger",
        "system" => "fas fa-cog text-secondary",
        _ => "fas fa-bell text-primary"
    };

    private string GetTimeAgo(DateTime dateTime)
    {
        var timeSpan = DateTime.Now - dateTime;
        
        return timeSpan.TotalMinutes switch
        {
            < 1 => "À l'instant",
            < 60 => $"Il y a {(int)timeSpan.TotalMinutes} min",
            < 1440 => $"Il y a {(int)timeSpan.TotalHours} h",
            _ => $"Il y a {(int)timeSpan.TotalDays} j"
        };
    }

    public async ValueTask DisposeAsync()
    {
        if (_hubConnection is not null)
        {
            await _hubConnection.DisposeAsync();
        }
    }

    public class NotificationDto
    {
        public int Id { get; set; }
        public string Title { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; }
        public bool IsRead { get; set; }
        public string? ActionUrl { get; set; }
    }
}

<style>
    .notification-center {
        position: relative;
        display: inline-block;
    }

    .notification-bell {
        position: relative;
        cursor: pointer;
        padding: 8px;
        border-radius: 50%;
        transition: background-color 0.2s;
    }

    .notification-bell:hover {
        background-color: rgba(0, 0, 0, 0.1);
    }

    .notification-badge {
        position: absolute;
        top: 0;
        right: 0;
        background-color: #dc3545;
        color: white;
        border-radius: 10px;
        padding: 2px 6px;
        font-size: 0.75rem;
        min-width: 18px;
        text-align: center;
    }

    .notification-dropdown {
        position: absolute;
        top: 100%;
        right: 0;
        width: 350px;
        max-height: 500px;
        background: white;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        z-index: 1000;
        overflow: hidden;
    }

    .notification-header {
        padding: 12px 16px;
        border-bottom: 1px solid #dee2e6;
        display: flex;
        justify-content: space-between;
        align-items: center;
        background-color: #f8f9fa;
    }

    .notification-list {
        max-height: 400px;
        overflow-y: auto;
    }

    .notification-item {
        display: flex;
        padding: 12px 16px;
        border-bottom: 1px solid #f1f3f4;
        cursor: pointer;
        transition: background-color 0.2s;
        position: relative;
    }

    .notification-item:hover {
        background-color: #f8f9fa;
    }

    .notification-item.unread {
        background-color: #e3f2fd;
    }

    .notification-icon {
        margin-right: 12px;
        font-size: 1.2rem;
        width: 24px;
        text-align: center;
    }

    .notification-content {
        flex: 1;
    }

    .notification-title {
        font-weight: 600;
        margin-bottom: 4px;
        color: #333;
    }

    .notification-message {
        font-size: 0.9rem;
        color: #666;
        margin-bottom: 4px;
    }

    .notification-time {
        font-size: 0.8rem;
        color: #999;
    }

    .unread-indicator {
        width: 8px;
        height: 8px;
        background-color: #007bff;
        border-radius: 50%;
        margin-left: 8px;
        margin-top: 8px;
    }

    .no-notifications {
        text-align: center;
        padding: 40px 20px;
        color: #999;
    }

    .no-notifications i {
        font-size: 2rem;
        margin-bottom: 10px;
    }

    .notification-footer {
        padding: 12px 16px;
        border-top: 1px solid #dee2e6;
        background-color: #f8f9fa;
    }
</style>
