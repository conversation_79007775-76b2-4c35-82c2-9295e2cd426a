using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using System.Text.Json;

namespace NafaPlace.Catalog.Application.Services;

public interface IProductApprovalNotificationService
{
    Task NotifyProductSubmittedForApprovalAsync(int productId, string sellerId, string submittedBy);
    Task NotifyProductApprovedAsync(int productId, string sellerId, string approvedBy);
    Task NotifyProductRejectedAsync(int productId, string sellerId, string rejectedBy, string rejectionReason);
    Task NotifyChangesRequestedAsync(int productId, string sellerId, string requestedBy, string changeRequests);
    Task NotifyApprovalTimeoutAsync(int productId, string sellerId);
    Task NotifyAdminsOfPendingApprovalsAsync(List<int> productIds);
    Task NotifySellerOfBulkActionAsync(string sellerId, List<int> productIds, string action, string? reason = null);
}

public class ProductApprovalNotificationService : IProductApprovalNotificationService
{
    private readonly ILogger<ProductApprovalNotificationService> _logger;
    private readonly IConfiguration _configuration;
    private readonly HttpClient _httpClient;
    private readonly NotificationSettings _settings;

    public ProductApprovalNotificationService(
        ILogger<ProductApprovalNotificationService> logger,
        IConfiguration configuration,
        HttpClient httpClient)
    {
        _logger = logger;
        _configuration = configuration;
        _httpClient = httpClient;
        _settings = new NotificationSettings
        {
            NotificationServiceUrl = configuration["Services:Notifications:BaseUrl"] ?? "http://localhost:5003",
            EnableEmailNotifications = configuration.GetValue<bool>("Notifications:EnableEmail", true),
            EnablePushNotifications = configuration.GetValue<bool>("Notifications:EnablePush", true),
            EnableSmsNotifications = configuration.GetValue<bool>("Notifications:EnableSms", false)
        };
    }

    public async Task NotifyProductSubmittedForApprovalAsync(int productId, string sellerId, string submittedBy)
    {
        try
        {
            // Notification au vendeur
            var sellerNotification = new NotificationRequest
            {
                UserId = sellerId,
                Title = "Produit soumis pour approbation",
                Message = $"Votre produit #{productId} a été soumis pour approbation. Vous recevrez une notification une fois qu'il sera examiné.",
                Type = "product_submitted",
                Priority = NotificationPriority.Normal,
                ActionUrl = $"/seller/products/{productId}",
                Data = new Dictionary<string, object>
                {
                    { "productId", productId },
                    { "action", "submitted" },
                    { "submittedBy", submittedBy }
                }
            };

            await SendNotificationAsync(sellerNotification);

            // Notification aux administrateurs
            var adminNotification = new NotificationRequest
            {
                UserId = "admin", // Notification générale aux admins
                Title = "Nouveau produit à approuver",
                Message = $"Un nouveau produit #{productId} a été soumis pour approbation par le vendeur {sellerId}.",
                Type = "product_pending_approval",
                Priority = NotificationPriority.High,
                ActionUrl = $"/admin/products/{productId}/approve",
                Data = new Dictionary<string, object>
                {
                    { "productId", productId },
                    { "sellerId", sellerId },
                    { "action", "pending_approval" }
                }
            };

            await SendNotificationAsync(adminNotification);

            _logger.LogInformation("Notifications sent for product submission: ProductId={ProductId}, SellerId={SellerId}", 
                productId, sellerId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending product submission notifications for ProductId={ProductId}", productId);
        }
    }

    public async Task NotifyProductApprovedAsync(int productId, string sellerId, string approvedBy)
    {
        try
        {
            var notification = new NotificationRequest
            {
                UserId = sellerId,
                Title = "Produit approuvé !",
                Message = $"Félicitations ! Votre produit #{productId} a été approuvé et est maintenant visible sur la plateforme.",
                Type = "product_approved",
                Priority = NotificationPriority.High,
                ActionUrl = $"/seller/products/{productId}",
                Data = new Dictionary<string, object>
                {
                    { "productId", productId },
                    { "action", "approved" },
                    { "approvedBy", approvedBy },
                    { "approvedAt", DateTime.UtcNow }
                }
            };

            await SendNotificationAsync(notification);

            // Notification email si activée
            if (_settings.EnableEmailNotifications)
            {
                await SendEmailNotificationAsync(sellerId, "Produit approuvé", 
                    $"Votre produit #{productId} a été approuvé par {approvedBy}. Il est maintenant visible sur NafaPlace.");
            }

            _logger.LogInformation("Product approval notification sent: ProductId={ProductId}, SellerId={SellerId}, ApprovedBy={ApprovedBy}", 
                productId, sellerId, approvedBy);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending product approval notification for ProductId={ProductId}", productId);
        }
    }

    public async Task NotifyProductRejectedAsync(int productId, string sellerId, string rejectedBy, string rejectionReason)
    {
        try
        {
            var notification = new NotificationRequest
            {
                UserId = sellerId,
                Title = "Produit rejeté",
                Message = $"Votre produit #{productId} a été rejeté. Raison: {rejectionReason}. Vous pouvez le modifier et le soumettre à nouveau.",
                Type = "product_rejected",
                Priority = NotificationPriority.High,
                ActionUrl = $"/seller/products/{productId}/edit",
                Data = new Dictionary<string, object>
                {
                    { "productId", productId },
                    { "action", "rejected" },
                    { "rejectedBy", rejectedBy },
                    { "rejectionReason", rejectionReason },
                    { "rejectedAt", DateTime.UtcNow }
                }
            };

            await SendNotificationAsync(notification);

            // Notification email si activée
            if (_settings.EnableEmailNotifications)
            {
                await SendEmailNotificationAsync(sellerId, "Produit rejeté", 
                    $"Votre produit #{productId} a été rejeté par {rejectedBy}. Raison: {rejectionReason}. Vous pouvez le modifier et le soumettre à nouveau.");
            }

            _logger.LogInformation("Product rejection notification sent: ProductId={ProductId}, SellerId={SellerId}, RejectedBy={RejectedBy}", 
                productId, sellerId, rejectedBy);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending product rejection notification for ProductId={ProductId}", productId);
        }
    }

    public async Task NotifyChangesRequestedAsync(int productId, string sellerId, string requestedBy, string changeRequests)
    {
        try
        {
            var notification = new NotificationRequest
            {
                UserId = sellerId,
                Title = "Modifications demandées",
                Message = $"Des modifications sont demandées pour votre produit #{productId}. Détails: {changeRequests}",
                Type = "product_changes_requested",
                Priority = NotificationPriority.Normal,
                ActionUrl = $"/seller/products/{productId}/edit",
                Data = new Dictionary<string, object>
                {
                    { "productId", productId },
                    { "action", "changes_requested" },
                    { "requestedBy", requestedBy },
                    { "changeRequests", changeRequests },
                    { "requestedAt", DateTime.UtcNow }
                }
            };

            await SendNotificationAsync(notification);

            _logger.LogInformation("Product changes requested notification sent: ProductId={ProductId}, SellerId={SellerId}", 
                productId, sellerId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending product changes requested notification for ProductId={ProductId}", productId);
        }
    }

    public async Task NotifyApprovalTimeoutAsync(int productId, string sellerId)
    {
        try
        {
            // Notification au vendeur
            var sellerNotification = new NotificationRequest
            {
                UserId = sellerId,
                Title = "Délai d'approbation dépassé",
                Message = $"L'approbation de votre produit #{productId} prend plus de temps que prévu. Nous examinons votre demande.",
                Type = "product_approval_timeout",
                Priority = NotificationPriority.Normal,
                ActionUrl = $"/seller/products/{productId}",
                Data = new Dictionary<string, object>
                {
                    { "productId", productId },
                    { "action", "approval_timeout" }
                }
            };

            await SendNotificationAsync(sellerNotification);

            // Notification aux administrateurs pour escalade
            var adminNotification = new NotificationRequest
            {
                UserId = "admin",
                Title = "Approbation en retard",
                Message = $"Le produit #{productId} attend une approbation depuis plus de 72 heures. Action requise.",
                Type = "product_approval_escalation",
                Priority = NotificationPriority.Urgent,
                ActionUrl = $"/admin/products/{productId}/approve",
                Data = new Dictionary<string, object>
                {
                    { "productId", productId },
                    { "sellerId", sellerId },
                    { "action", "escalation" }
                }
            };

            await SendNotificationAsync(adminNotification);

            _logger.LogInformation("Approval timeout notifications sent for ProductId={ProductId}", productId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending approval timeout notifications for ProductId={ProductId}", productId);
        }
    }

    public async Task NotifyAdminsOfPendingApprovalsAsync(List<int> productIds)
    {
        try
        {
            if (!productIds.Any()) return;

            var notification = new NotificationRequest
            {
                UserId = "admin",
                Title = $"{productIds.Count} produits en attente d'approbation",
                Message = $"Il y a {productIds.Count} produits qui attendent votre approbation.",
                Type = "pending_approvals_summary",
                Priority = NotificationPriority.Normal,
                ActionUrl = "/admin/products/pending",
                Data = new Dictionary<string, object>
                {
                    { "productIds", productIds },
                    { "count", productIds.Count },
                    { "action", "pending_summary" }
                }
            };

            await SendNotificationAsync(notification);

            _logger.LogInformation("Pending approvals summary notification sent: Count={Count}", productIds.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending pending approvals summary notification");
        }
    }

    public async Task NotifySellerOfBulkActionAsync(string sellerId, List<int> productIds, string action, string? reason = null)
    {
        try
        {
            var actionText = action.ToLower() switch
            {
                "approved" => "approuvés",
                "rejected" => "rejetés",
                _ => "traités"
            };

            var message = $"{productIds.Count} de vos produits ont été {actionText}.";
            if (!string.IsNullOrEmpty(reason))
            {
                message += $" Raison: {reason}";
            }

            var notification = new NotificationRequest
            {
                UserId = sellerId,
                Title = $"Action groupée sur vos produits",
                Message = message,
                Type = "bulk_product_action",
                Priority = NotificationPriority.Normal,
                ActionUrl = "/seller/products",
                Data = new Dictionary<string, object>
                {
                    { "productIds", productIds },
                    { "action", action },
                    { "reason", reason ?? "" },
                    { "count", productIds.Count }
                }
            };

            await SendNotificationAsync(notification);

            _logger.LogInformation("Bulk action notification sent: SellerId={SellerId}, Action={Action}, Count={Count}", 
                sellerId, action, productIds.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending bulk action notification for SellerId={SellerId}", sellerId);
        }
    }

    // Méthodes privées
    private async Task SendNotificationAsync(NotificationRequest notification)
    {
        try
        {
            var json = JsonSerializer.Serialize(notification);
            var content = new StringContent(json, System.Text.Encoding.UTF8, "application/json");
            
            var response = await _httpClient.PostAsync($"{_settings.NotificationServiceUrl}/api/notifications", content);
            
            if (!response.IsSuccessStatusCode)
            {
                _logger.LogWarning("Failed to send notification. Status: {StatusCode}, Response: {Response}", 
                    response.StatusCode, await response.Content.ReadAsStringAsync());
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending notification to service");
        }
    }

    private async Task SendEmailNotificationAsync(string userId, string subject, string message)
    {
        try
        {
            var emailRequest = new
            {
                UserId = userId,
                Subject = subject,
                Message = message,
                Type = "product_approval"
            };

            var json = JsonSerializer.Serialize(emailRequest);
            var content = new StringContent(json, System.Text.Encoding.UTF8, "application/json");
            
            var response = await _httpClient.PostAsync($"{_settings.NotificationServiceUrl}/api/notifications/email", content);
            
            if (!response.IsSuccessStatusCode)
            {
                _logger.LogWarning("Failed to send email notification. Status: {StatusCode}", response.StatusCode);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending email notification");
        }
    }
}

// Classes de support
public class NotificationRequest
{
    public string UserId { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty;
    public NotificationPriority Priority { get; set; } = NotificationPriority.Normal;
    public string? ActionUrl { get; set; }
    public Dictionary<string, object> Data { get; set; } = new();
    public DateTime? ScheduledFor { get; set; }
    public bool RequireAcknowledgment { get; set; } = false;
}

public enum NotificationPriority
{
    Low = 0,
    Normal = 1,
    High = 2,
    Urgent = 3
}

public class NotificationSettings
{
    public string NotificationServiceUrl { get; set; } = string.Empty;
    public bool EnableEmailNotifications { get; set; } = true;
    public bool EnablePushNotifications { get; set; } = true;
    public bool EnableSmsNotifications { get; set; } = false;
    public int MaxRetryAttempts { get; set; } = 3;
    public int RetryDelaySeconds { get; set; } = 30;
}
