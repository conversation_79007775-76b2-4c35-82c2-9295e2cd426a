using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using SendGrid;
using SendGrid.Helpers.Mail;
using NafaPlace.Notifications.Application.Interfaces;
using System.Text.Json;

namespace NafaPlace.Notifications.Infrastructure.Services;

public class SendGridEmailService : IEmailService
{
    private readonly ISendGridClient _sendGridClient;
    private readonly SendGridSettings _settings;
    private readonly ILogger<SendGridEmailService> _logger;

    public SendGridEmailService(
        ISendGridClient sendGridClient,
        IOptions<SendGridSettings> settings,
        ILogger<SendGridEmailService> logger)
    {
        _sendGridClient = sendGridClient;
        _settings = settings.Value;
        _logger = logger;
    }

    public async Task<bool> SendEmailAsync(string to, string subject, string body, bool isHtml = true)
    {
        try
        {
            var from = new EmailAddress(_settings.FromEmail, _settings.FromName);
            var toEmail = new EmailAddress(to);
            
            var msg = MailHelper.CreateSingleEmail(from, toEmail, subject, 
                isHtml ? null : body, isHtml ? body : null);

            // Ajouter des tags pour le tracking
            msg.AddCategory("NafaPlace");
            msg.AddCustomArg("environment", _settings.Environment);

            var response = await _sendGridClient.SendEmailAsync(msg);

            if (response.IsSuccessStatusCode)
            {
                _logger.LogInformation("✅ Email sent successfully to {To} via SendGrid", to);
                return true;
            }
            else
            {
                var responseBody = await response.Body.ReadAsStringAsync();
                _logger.LogError("❌ SendGrid API error: {StatusCode} - {Response}", response.StatusCode, responseBody);
                return false;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ Failed to send email to {To} via SendGrid", to);
            return false;
        }
    }

    public async Task<bool> SendEmailWithTemplateAsync(string to, string templateCode, Dictionary<string, object> variables)
    {
        try
        {
            var templateId = GetTemplateId(templateCode);
            if (string.IsNullOrEmpty(templateId))
            {
                _logger.LogWarning("Template not found: {TemplateCode}", templateCode);
                return false;
            }

            var from = new EmailAddress(_settings.FromEmail, _settings.FromName);
            var toEmail = new EmailAddress(to);

            var msg = MailHelper.CreateSingleTemplateEmail(from, toEmail, templateId, variables);
            
            // Ajouter des tags pour le tracking
            msg.AddCategory("NafaPlace");
            msg.AddCategory($"Template-{templateCode}");
            msg.AddCustomArg("template", templateCode);
            msg.AddCustomArg("environment", _settings.Environment);

            var response = await _sendGridClient.SendEmailAsync(msg);

            if (response.IsSuccessStatusCode)
            {
                _logger.LogInformation("✅ Templated email sent successfully to {To} with template {TemplateCode}", to, templateCode);
                return true;
            }
            else
            {
                var responseBody = await response.Body.ReadAsStringAsync();
                _logger.LogError("❌ SendGrid template API error: {StatusCode} - {Response}", response.StatusCode, responseBody);
                return false;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ Failed to send templated email to {To} with template {TemplateCode}", to, templateCode);
            return false;
        }
    }

    public async Task<bool> SendBulkEmailAsync(List<string> recipients, string subject, string body, bool isHtml = true)
    {
        try
        {
            var from = new EmailAddress(_settings.FromEmail, _settings.FromName);
            var tos = recipients.Select(email => new EmailAddress(email)).ToList();

            var msg = MailHelper.CreateSingleEmailToMultipleRecipients(from, tos, subject,
                isHtml ? null : body, isHtml ? body : null);

            // Ajouter des tags pour le tracking
            msg.AddCategory("NafaPlace");
            msg.AddCategory("Bulk");
            msg.AddCustomArg("bulk_count", recipients.Count.ToString());
            msg.AddCustomArg("environment", _settings.Environment);

            var response = await _sendGridClient.SendEmailAsync(msg);

            if (response.IsSuccessStatusCode)
            {
                _logger.LogInformation("✅ Bulk email sent successfully to {Count} recipients", recipients.Count);
                return true;
            }
            else
            {
                var responseBody = await response.Body.ReadAsStringAsync();
                _logger.LogError("❌ SendGrid bulk API error: {StatusCode} - {Response}", response.StatusCode, responseBody);
                return false;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ Failed to send bulk email to {Count} recipients", recipients.Count);
            return false;
        }
    }

    public async Task<bool> IsEmailValidAsync(string email)
    {
        await Task.CompletedTask;
        
        if (string.IsNullOrWhiteSpace(email))
            return false;

        try
        {
            var addr = new System.Net.Mail.MailAddress(email);
            return addr.Address == email;
        }
        catch
        {
            return false;
        }
    }

    private string? GetTemplateId(string templateCode)
    {
        // Mapping des codes de template vers les IDs SendGrid
        return templateCode switch
        {
            "order_confirmation" => _settings.Templates?.OrderConfirmation,
            "order_status_update" => _settings.Templates?.OrderStatusUpdate,
            "payment_confirmation" => _settings.Templates?.PaymentConfirmation,
            "new_review" => _settings.Templates?.NewReview,
            "low_stock_alert" => _settings.Templates?.LowStockAlert,
            "welcome" => _settings.Templates?.Welcome,
            "password_reset" => _settings.Templates?.PasswordReset,
            "account_verification" => _settings.Templates?.AccountVerification,
            _ => null
        };
    }
}

public class SendGridSettings
{
    public string ApiKey { get; set; } = string.Empty;
    public string FromEmail { get; set; } = string.Empty;
    public string FromName { get; set; } = "NafaPlace";
    public string Environment { get; set; } = "Development";
    public SendGridTemplates? Templates { get; set; }
}

public class SendGridTemplates
{
    public string? OrderConfirmation { get; set; }
    public string? OrderStatusUpdate { get; set; }
    public string? PaymentConfirmation { get; set; }
    public string? NewReview { get; set; }
    public string? LowStockAlert { get; set; }
    public string? Welcome { get; set; }
    public string? PasswordReset { get; set; }
    public string? AccountVerification { get; set; }
}
