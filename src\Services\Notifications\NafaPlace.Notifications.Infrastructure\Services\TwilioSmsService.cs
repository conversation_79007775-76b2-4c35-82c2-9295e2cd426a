using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Twilio;
using Twilio.Rest.Api.V2010.Account;
using Twilio.Types;
using NafaPlace.Notifications.Application.Interfaces;
using System.Text.RegularExpressions;

namespace NafaPlace.Notifications.Infrastructure.Services;

public class TwilioSmsService : ISmsService
{
    private readonly TwilioSettings _settings;
    private readonly ILogger<TwilioSmsService> _logger;

    public TwilioSmsService(
        IOptions<TwilioSettings> settings,
        ILogger<TwilioSmsService> logger)
    {
        _settings = settings.Value;
        _logger = logger;

        // Initialiser Twilio
        TwilioClient.Init(_settings.AccountSid, _settings.AuthToken);
    }

    public async Task<bool> SendSmsAsync(string phoneNumber, string message)
    {
        try
        {
            // Valider et formater le numéro de téléphone
            var formattedNumber = FormatGuineanPhoneNumber(phoneNumber);
            if (string.IsNullOrEmpty(formattedNumber))
            {
                _logger.LogWarning("Invalid phone number format: {PhoneNumber}", phoneNumber);
                return false;
            }

            var messageResource = await MessageResource.CreateAsync(
                body: message,
                from: new PhoneNumber(_settings.FromPhoneNumber),
                to: new PhoneNumber(formattedNumber)
            );

            if (messageResource.Status == MessageResource.StatusEnum.Sent ||
                messageResource.Status == MessageResource.StatusEnum.Queued ||
                messageResource.Status == MessageResource.StatusEnum.Accepted)
            {
                _logger.LogInformation("✅ SMS sent successfully to {PhoneNumber} via Twilio. SID: {MessageSid}", 
                    formattedNumber, messageResource.Sid);
                return true;
            }
            else
            {
                _logger.LogError("❌ Twilio SMS failed with status: {Status} for {PhoneNumber}", 
                    messageResource.Status, formattedNumber);
                return false;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ Failed to send SMS to {PhoneNumber} via Twilio", phoneNumber);
            return false;
        }
    }

    public async Task<bool> SendSmsWithTemplateAsync(string phoneNumber, string templateCode, Dictionary<string, object> variables)
    {
        try
        {
            var template = GetSmsTemplate(templateCode);
            if (string.IsNullOrEmpty(template))
            {
                _logger.LogWarning("SMS template not found: {TemplateCode}", templateCode);
                return false;
            }

            // Remplacer les variables dans le template
            var message = ProcessTemplate(template, variables);
            
            return await SendSmsAsync(phoneNumber, message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ Failed to send templated SMS to {PhoneNumber} with template {TemplateCode}", 
                phoneNumber, templateCode);
            return false;
        }
    }

    public async Task<bool> SendBulkSmsAsync(List<string> phoneNumbers, string message)
    {
        var successCount = 0;
        var tasks = new List<Task<bool>>();

        // Limiter le nombre de SMS simultanés pour éviter les limites de taux
        var semaphore = new SemaphoreSlim(_settings.MaxConcurrentSms, _settings.MaxConcurrentSms);

        foreach (var phoneNumber in phoneNumbers)
        {
            tasks.Add(SendSmsWithSemaphore(phoneNumber, message, semaphore));
        }

        var results = await Task.WhenAll(tasks);
        successCount = results.Count(r => r);

        _logger.LogInformation("📱 Bulk SMS sent: {SuccessCount}/{TotalCount} via Twilio", 
            successCount, phoneNumbers.Count);
        
        return successCount == phoneNumbers.Count;
    }

    public async Task<bool> IsPhoneNumberValidAsync(string phoneNumber)
    {
        await Task.CompletedTask;
        
        if (string.IsNullOrWhiteSpace(phoneNumber))
            return false;

        // Validation pour les numéros guinéens
        var formatted = FormatGuineanPhoneNumber(phoneNumber);
        return !string.IsNullOrEmpty(formatted);
    }

    private async Task<bool> SendSmsWithSemaphore(string phoneNumber, string message, SemaphoreSlim semaphore)
    {
        await semaphore.WaitAsync();
        try
        {
            return await SendSmsAsync(phoneNumber, message);
        }
        finally
        {
            semaphore.Release();
        }
    }

    private string FormatGuineanPhoneNumber(string phoneNumber)
    {
        // Nettoyer le numéro
        var cleaned = Regex.Replace(phoneNumber, @"[^\d+]", "");
        
        // Formats acceptés pour la Guinée:
        // +224XXXXXXXXX (format international)
        // 224XXXXXXXXX (sans +)
        // 6XXXXXXXX ou 7XXXXXXXX (format local)
        
        if (cleaned.StartsWith("+224") && cleaned.Length == 13)
        {
            return cleaned; // Déjà au bon format
        }
        else if (cleaned.StartsWith("224") && cleaned.Length == 12)
        {
            return $"+{cleaned}";
        }
        else if ((cleaned.StartsWith("6") || cleaned.StartsWith("7")) && cleaned.Length == 9)
        {
            return $"+224{cleaned}";
        }
        
        return string.Empty; // Format non reconnu
    }

    private string? GetSmsTemplate(string templateCode)
    {
        return templateCode switch
        {
            "order_confirmation" => "Votre commande #{OrderId} a été confirmée. Montant: {Amount} GNF. Merci de votre confiance! - NafaPlace",
            "order_shipped" => "Bonne nouvelle! Votre commande #{OrderId} a été expédiée. Suivi: {TrackingNumber} - NafaPlace",
            "order_delivered" => "Votre commande #{OrderId} a été livrée. Merci d'avoir choisi NafaPlace!",
            "payment_confirmation" => "Paiement confirmé pour votre commande #{OrderId}. Montant: {Amount} GNF - NafaPlace",
            "low_stock_alert" => "Stock faible: {ProductName} - Il ne reste que {Stock} unités. Réapprovisionnez rapidement! - NafaPlace",
            "account_verification" => "Code de vérification NafaPlace: {Code}. Ne partagez ce code avec personne.",
            "password_reset" => "Code de réinitialisation NafaPlace: {Code}. Valide pendant 15 minutes.",
            "promotion" => "🎉 Offre spéciale NafaPlace: {OfferTitle}. Utilisez le code {PromoCode} avant le {ExpiryDate}!",
            _ => null
        };
    }

    private string ProcessTemplate(string template, Dictionary<string, object> variables)
    {
        var result = template;
        
        foreach (var variable in variables)
        {
            var placeholder = $"{{{variable.Key}}}";
            result = result.Replace(placeholder, variable.Value?.ToString() ?? "");
        }
        
        return result;
    }
}

public class TwilioSettings
{
    public string AccountSid { get; set; } = string.Empty;
    public string AuthToken { get; set; } = string.Empty;
    public string FromPhoneNumber { get; set; } = string.Empty;
    public int MaxConcurrentSms { get; set; } = 5;
    public bool EnableDeliveryReports { get; set; } = true;
    public string Environment { get; set; } = "Development";
}
