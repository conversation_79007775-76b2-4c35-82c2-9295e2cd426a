using NafaPlace.Analytics.Application.DTOs;

namespace NafaPlace.Analytics.Application.Interfaces;

public interface IAnalyticsService
{
    Task<SellerDashboardDto> GetSellerDashboardAsync(AnalyticsRequest request);
    Task<AdminDashboardDto> GetAdminDashboardAsync(AnalyticsRequest request);
    Task<SalesAnalyticsDto> GetSalesAnalyticsAsync(AnalyticsRequest request);
    Task<ProductPerformanceDto> GetProductPerformanceAsync(AnalyticsRequest request);
    Task<CustomerAnalyticsDto> GetCustomerAnalyticsAsync(AnalyticsRequest request);
    Task<FinancialAnalyticsDto> GetFinancialAnalyticsAsync(AnalyticsRequest request);
    Task<InventoryAnalyticsDto> GetInventoryAnalyticsAsync(AnalyticsRequest request);
    Task<ComparisonAnalyticsDto> GetComparisonAnalyticsAsync(ComparisonRequest request);
    Task<RealTimeMetricsDto> GetRealTimeMetricsAsync(int? sellerId = null);
    Task<byte[]> ExportAnalyticsAsync(ExportRequest request);
}

namespace NafaPlace.Analytics.Application.DTOs;

// Requêtes de base
public class AnalyticsRequest
{
    public int? SellerId { get; set; }
    public DateTime StartDate { get; set; } = DateTime.UtcNow.AddDays(-30);
    public DateTime EndDate { get; set; } = DateTime.UtcNow;
    public string GroupBy { get; set; } = "day"; // day, week, month, year
    public int Limit { get; set; } = 10;
    public string? Category { get; set; }
    public string? Region { get; set; }
}

public class ComparisonRequest
{
    public int? SellerId { get; set; }
    public AnalyticsRequest CurrentPeriod { get; set; } = new();
    public AnalyticsRequest PreviousPeriod { get; set; } = new();
}

public class ExportRequest
{
    public int? SellerId { get; set; }
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
    public string Format { get; set; } = "excel"; // excel, csv, pdf
    public string ReportType { get; set; } = "dashboard"; // dashboard, sales, products, customers
    public List<string> Metrics { get; set; } = new();
}

// Dashboard DTOs
public class SellerDashboardDto
{
    public SalesMetricsDto Sales { get; set; } = new();
    public OrderMetricsDto Orders { get; set; } = new();
    public ProductMetricsDto Products { get; set; } = new();
    public CustomerMetricsDto Customers { get; set; } = new();
    public List<ChartDataDto> SalesChart { get; set; } = new();
    public List<TopProductDto> TopProducts { get; set; } = new();
    public List<RecentOrderDto> RecentOrders { get; set; } = new();
    public InventoryAlertDto InventoryAlerts { get; set; } = new();
}

public class AdminDashboardDto
{
    public PlatformMetricsDto Platform { get; set; } = new();
    public SalesMetricsDto Sales { get; set; } = new();
    public UserMetricsDto Users { get; set; } = new();
    public OrderMetricsDto Orders { get; set; } = new();
    public List<ChartDataDto> SalesChart { get; set; } = new();
    public List<TopSellerDto> TopSellers { get; set; } = new();
    public List<CategoryPerformanceDto> CategoryPerformance { get; set; } = new();
    public List<RegionPerformanceDto> RegionPerformance { get; set; } = new();
}

// Métriques de base
public class SalesMetricsDto
{
    public decimal TotalRevenue { get; set; }
    public decimal RevenueGrowth { get; set; }
    public int TotalOrders { get; set; }
    public decimal OrdersGrowth { get; set; }
    public decimal AverageOrderValue { get; set; }
    public decimal AovGrowth { get; set; }
    public decimal ConversionRate { get; set; }
    public decimal ConversionGrowth { get; set; }
    public decimal RefundRate { get; set; }
    public decimal RefundAmount { get; set; }
}

public class OrderMetricsDto
{
    public int TotalOrders { get; set; }
    public int PendingOrders { get; set; }
    public int ProcessingOrders { get; set; }
    public int ShippedOrders { get; set; }
    public int DeliveredOrders { get; set; }
    public int CancelledOrders { get; set; }
    public int RefundedOrders { get; set; }
    public decimal CancellationRate { get; set; }
    public decimal FulfillmentRate { get; set; }
    public double AverageProcessingTime { get; set; } // en heures
}

public class ProductMetricsDto
{
    public int TotalProducts { get; set; }
    public int ActiveProducts { get; set; }
    public int OutOfStockProducts { get; set; }
    public int LowStockProducts { get; set; }
    public int NewProducts { get; set; }
    public decimal AverageRating { get; set; }
    public int TotalReviews { get; set; }
    public int ProductViews { get; set; }
    public decimal ViewToSaleConversion { get; set; }
}

public class CustomerMetricsDto
{
    public int TotalCustomers { get; set; }
    public int NewCustomers { get; set; }
    public int ReturningCustomers { get; set; }
    public decimal CustomerRetentionRate { get; set; }
    public decimal CustomerLifetimeValue { get; set; }
    public decimal AverageOrdersPerCustomer { get; set; }
    public int ActiveCustomers { get; set; }
    public decimal ChurnRate { get; set; }
}

public class PlatformMetricsDto
{
    public int TotalSellers { get; set; }
    public int ActiveSellers { get; set; }
    public int NewSellers { get; set; }
    public int TotalUsers { get; set; }
    public int ActiveUsers { get; set; }
    public decimal PlatformCommission { get; set; }
    public decimal TotalGMV { get; set; } // Gross Merchandise Value
    public decimal GmvGrowth { get; set; }
}

public class UserMetricsDto
{
    public int TotalUsers { get; set; }
    public int ActiveUsers { get; set; }
    public int NewUsers { get; set; }
    public int VerifiedUsers { get; set; }
    public decimal UserGrowthRate { get; set; }
    public double AverageSessionDuration { get; set; }
    public int DailyActiveUsers { get; set; }
    public int MonthlyActiveUsers { get; set; }
}

// Analytics spécialisés
public class SalesAnalyticsDto
{
    public List<ChartDataDto> SalesOverTime { get; set; } = new();
    public List<CategorySalesDto> SalesByCategory { get; set; } = new();
    public List<RegionSalesDto> SalesByRegion { get; set; } = new();
    public List<PaymentMethodDto> SalesByPaymentMethod { get; set; } = new();
    public SalesMetricsDto Summary { get; set; } = new();
    public List<SalesTrendDto> Trends { get; set; } = new();
}

public class ProductPerformanceDto
{
    public List<TopProductDto> TopSellingProducts { get; set; } = new();
    public List<TopProductDto> TopRevenueProducts { get; set; } = new();
    public List<ProductDto> LowPerformingProducts { get; set; } = new();
    public List<ProductDto> TrendingProducts { get; set; } = new();
    public List<CategoryPerformanceDto> CategoryPerformance { get; set; } = new();
    public ProductMetricsDto Summary { get; set; } = new();
}

public class CustomerAnalyticsDto
{
    public List<CustomerSegmentDto> CustomerSegments { get; set; } = new();
    public List<ChartDataDto> CustomerAcquisition { get; set; } = new();
    public List<ChartDataDto> CustomerRetention { get; set; } = new();
    public List<TopCustomerDto> TopCustomers { get; set; } = new();
    public CustomerMetricsDto Summary { get; set; } = new();
    public List<CustomerBehaviorDto> BehaviorInsights { get; set; } = new();
}

public class FinancialAnalyticsDto
{
    public List<ChartDataDto> RevenueOverTime { get; set; } = new();
    public List<ChartDataDto> ProfitMargins { get; set; } = new();
    public List<ExpenseDto> Expenses { get; set; } = new();
    public List<TaxDto> Taxes { get; set; } = new();
    public FinancialMetricsDto Summary { get; set; } = new();
    public List<CashFlowDto> CashFlow { get; set; } = new();
}

public class InventoryAnalyticsDto
{
    public List<ProductStockDto> LowStockProducts { get; set; } = new();
    public List<ProductStockDto> OutOfStockProducts { get; set; } = new();
    public List<ProductStockDto> OverstockedProducts { get; set; } = new();
    public List<ChartDataDto> StockMovements { get; set; } = new();
    public InventoryMetricsDto Summary { get; set; } = new();
    public List<StockAlertDto> Alerts { get; set; } = new();
}

public class ComparisonAnalyticsDto
{
    public SalesMetricsDto CurrentPeriod { get; set; } = new();
    public SalesMetricsDto PreviousPeriod { get; set; } = new();
    public ComparisonMetricsDto Comparison { get; set; } = new();
    public List<ChartDataDto> ComparisonChart { get; set; } = new();
}

public class RealTimeMetricsDto
{
    public int OnlineUsers { get; set; }
    public int TodayOrders { get; set; }
    public decimal TodayRevenue { get; set; }
    public int PendingOrders { get; set; }
    public int LowStockAlerts { get; set; }
    public List<RecentActivityDto> RecentActivities { get; set; } = new();
    public DateTime LastUpdated { get; set; }
}

// DTOs de support
public class ChartDataDto
{
    public string Label { get; set; } = string.Empty;
    public decimal Value { get; set; }
    public DateTime Date { get; set; }
    public string? Category { get; set; }
    public Dictionary<string, object>? Metadata { get; set; }
}

public class TopProductDto
{
    public int ProductId { get; set; }
    public string Name { get; set; } = string.Empty;
    public string ImageUrl { get; set; } = string.Empty;
    public int UnitsSold { get; set; }
    public decimal Revenue { get; set; }
    public string Category { get; set; } = string.Empty;
    public decimal Rating { get; set; }
    public int ReviewCount { get; set; }
}

public class TopSellerDto
{
    public int SellerId { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public decimal Revenue { get; set; }
    public int OrderCount { get; set; }
    public decimal Rating { get; set; }
    public int ProductCount { get; set; }
}

public class CategoryPerformanceDto
{
    public string CategoryName { get; set; } = string.Empty;
    public decimal Revenue { get; set; }
    public int OrderCount { get; set; }
    public int ProductCount { get; set; }
    public decimal GrowthRate { get; set; }
    public decimal MarketShare { get; set; }
}

public class RegionPerformanceDto
{
    public string Region { get; set; } = string.Empty;
    public decimal Revenue { get; set; }
    public int OrderCount { get; set; }
    public int CustomerCount { get; set; }
    public decimal GrowthRate { get; set; }
    public decimal AverageOrderValue { get; set; }
}

public class RecentOrderDto
{
    public int OrderId { get; set; }
    public string CustomerName { get; set; } = string.Empty;
    public decimal Amount { get; set; }
    public string Status { get; set; } = string.Empty;
    public DateTime OrderDate { get; set; }
    public int ItemCount { get; set; }
}

public class InventoryAlertDto
{
    public int LowStockCount { get; set; }
    public int OutOfStockCount { get; set; }
    public int OverstockedCount { get; set; }
    public List<string> CriticalProducts { get; set; } = new();
}

// DTOs supplémentaires pour des analyses spécialisées
public class CategorySalesDto
{
    public string CategoryName { get; set; } = string.Empty;
    public decimal Revenue { get; set; }
    public int OrderCount { get; set; }
    public decimal Percentage { get; set; }
}

public class RegionSalesDto
{
    public string Region { get; set; } = string.Empty;
    public decimal Revenue { get; set; }
    public int OrderCount { get; set; }
    public decimal Percentage { get; set; }
}

public class PaymentMethodDto
{
    public string Method { get; set; } = string.Empty;
    public decimal Amount { get; set; }
    public int Count { get; set; }
    public decimal Percentage { get; set; }
}

public class SalesTrendDto
{
    public string Period { get; set; } = string.Empty;
    public decimal Value { get; set; }
    public decimal Change { get; set; }
    public string Trend { get; set; } = string.Empty; // up, down, stable
}

public class CustomerSegmentDto
{
    public string SegmentName { get; set; } = string.Empty;
    public int CustomerCount { get; set; }
    public decimal Revenue { get; set; }
    public decimal AverageOrderValue { get; set; }
    public decimal Percentage { get; set; }
}

public class TopCustomerDto
{
    public int CustomerId { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public decimal TotalSpent { get; set; }
    public int OrderCount { get; set; }
    public DateTime LastOrderDate { get; set; }
}

public class CustomerBehaviorDto
{
    public string Behavior { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public decimal Value { get; set; }
    public string Trend { get; set; } = string.Empty;
}

public class FinancialMetricsDto
{
    public decimal TotalRevenue { get; set; }
    public decimal TotalExpenses { get; set; }
    public decimal NetProfit { get; set; }
    public decimal ProfitMargin { get; set; }
    public decimal TotalTaxes { get; set; }
    public decimal CashFlow { get; set; }
}

public class ExpenseDto
{
    public string Category { get; set; } = string.Empty;
    public decimal Amount { get; set; }
    public DateTime Date { get; set; }
    public string Description { get; set; } = string.Empty;
}

public class TaxDto
{
    public string TaxType { get; set; } = string.Empty;
    public decimal Amount { get; set; }
    public decimal Rate { get; set; }
    public DateTime Date { get; set; }
}

public class CashFlowDto
{
    public DateTime Date { get; set; }
    public decimal Inflow { get; set; }
    public decimal Outflow { get; set; }
    public decimal NetFlow { get; set; }
    public decimal Balance { get; set; }
}

public class ProductStockDto
{
    public int ProductId { get; set; }
    public string Name { get; set; } = string.Empty;
    public string ImageUrl { get; set; } = string.Empty;
    public int CurrentStock { get; set; }
    public int MinimumStock { get; set; }
    public int MaximumStock { get; set; }
    public string Status { get; set; } = string.Empty;
    public DateTime LastRestocked { get; set; }
}

public class InventoryMetricsDto
{
    public int TotalProducts { get; set; }
    public int LowStockProducts { get; set; }
    public int OutOfStockProducts { get; set; }
    public int OverstockedProducts { get; set; }
    public decimal TotalInventoryValue { get; set; }
    public double AverageStockTurnover { get; set; }
}

public class StockAlertDto
{
    public int ProductId { get; set; }
    public string ProductName { get; set; } = string.Empty;
    public string AlertType { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; }
    public string Severity { get; set; } = string.Empty;
}

public class ComparisonMetricsDto
{
    public decimal RevenueChange { get; set; }
    public decimal OrdersChange { get; set; }
    public decimal CustomersChange { get; set; }
    public decimal AovChange { get; set; }
    public decimal ConversionChange { get; set; }
    public string OverallTrend { get; set; } = string.Empty;
}

public class RecentActivityDto
{
    public string Type { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public DateTime Timestamp { get; set; }
    public string? UserId { get; set; }
    public string? UserName { get; set; }
    public Dictionary<string, object>? Data { get; set; }
}

public class ProductDto
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string ImageUrl { get; set; } = string.Empty;
    public decimal Price { get; set; }
    public string Category { get; set; } = string.Empty;
    public int Stock { get; set; }
    public decimal Rating { get; set; }
    public int ReviewCount { get; set; }
}
