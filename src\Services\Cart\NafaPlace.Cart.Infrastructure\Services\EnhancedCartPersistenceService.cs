using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Logging;
using Microsoft.EntityFrameworkCore;
using NafaPlace.Cart.Domain;
using NafaPlace.Cart.Application;
using NafaPlace.Cart.Infrastructure.Data;
using System.Text.Json;

namespace NafaPlace.Cart.Infrastructure.Services;

public interface IEnhancedCartPersistenceService
{
    Task<ShoppingCart> GetCartAsync(string userId, bool isGuest = false);
    Task<ShoppingCart> SaveCartAsync(ShoppingCart cart, bool isGuest = false);
    Task<bool> MergeGuestCartAsync(string guestUserId, string authenticatedUserId);
    Task<bool> TransferCartAsync(string fromUserId, string toUserId);
    Task<bool> ClearCartAsync(string userId, bool isGuest = false);
    Task<bool> BackupCartAsync(string userId);
    Task<ShoppingCart?> RestoreCartAsync(string userId);
    Task<List<ShoppingCart>> GetExpiredGuestCartsAsync(TimeSpan expiration);
    Task<bool> CleanupExpiredCartsAsync();
    Task<CartPersistenceStats> GetPersistenceStatsAsync();
}

public class EnhancedCartPersistenceService : IEnhancedCartPersistenceService
{
    private readonly IDistributedCache _cache;
    private readonly CartDbContext _dbContext;
    private readonly ILogger<EnhancedCartPersistenceService> _logger;
    private readonly CartPersistenceSettings _settings;

    public EnhancedCartPersistenceService(
        IDistributedCache cache,
        CartDbContext dbContext,
        ILogger<EnhancedCartPersistenceService> logger,
        CartPersistenceSettings settings)
    {
        _cache = cache;
        _dbContext = dbContext;
        _logger = logger;
        _settings = settings;
    }

    public async Task<ShoppingCart> GetCartAsync(string userId, bool isGuest = false)
    {
        try
        {
            // Essayer d'abord le cache Redis
            var cachedCart = await GetCartFromCacheAsync(userId);
            if (cachedCart != null)
            {
                _logger.LogDebug("Cart loaded from cache for user {UserId}", userId);
                return cachedCart;
            }

            // Si pas en cache, essayer la base de données (pour les utilisateurs authentifiés)
            if (!isGuest)
            {
                var dbCart = await GetCartFromDatabaseAsync(userId);
                if (dbCart != null)
                {
                    // Remettre en cache
                    await SaveCartToCacheAsync(dbCart);
                    _logger.LogDebug("Cart loaded from database and cached for user {UserId}", userId);
                    return dbCart;
                }
            }

            // Créer un nouveau panier
            var newCart = new ShoppingCart(userId);
            await SaveCartAsync(newCart, isGuest);
            
            _logger.LogDebug("New cart created for user {UserId} (Guest: {IsGuest})", userId, isGuest);
            return newCart;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting cart for user {UserId}", userId);
            return new ShoppingCart(userId);
        }
    }

    public async Task<ShoppingCart> SaveCartAsync(ShoppingCart cart, bool isGuest = false)
    {
        try
        {
            // Mettre à jour les timestamps
            cart.UpdatedAt = DateTime.UtcNow;
            if (cart.CreatedAt == default)
                cart.CreatedAt = DateTime.UtcNow;

            // Sauvegarder en cache (toujours)
            await SaveCartToCacheAsync(cart);

            // Sauvegarder en base de données (seulement pour les utilisateurs authentifiés)
            if (!isGuest && !cart.UserId.StartsWith("guest_"))
            {
                await SaveCartToDatabaseAsync(cart);
            }

            _logger.LogDebug("Cart saved for user {UserId} (Guest: {IsGuest}, Items: {ItemCount})", 
                cart.UserId, isGuest, cart.Items.Count);

            return cart;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error saving cart for user {UserId}", cart.UserId);
            throw;
        }
    }

    public async Task<bool> MergeGuestCartAsync(string guestUserId, string authenticatedUserId)
    {
        try
        {
            _logger.LogInformation("Starting cart merge: Guest {GuestUserId} -> Authenticated {AuthUserId}", 
                guestUserId, authenticatedUserId);

            var guestCart = await GetCartAsync(guestUserId, true);
            var userCart = await GetCartAsync(authenticatedUserId, false);

            if (guestCart?.Items?.Any() != true)
            {
                _logger.LogDebug("No items in guest cart to merge");
                return false;
            }

            var mergedItems = 0;
            var conflictResolutions = new List<string>();

            foreach (var guestItem in guestCart.Items)
            {
                var existingItem = userCart.Items.FirstOrDefault(i => 
                    i.ProductId == guestItem.ProductId && 
                    i.VariantId == guestItem.VariantId);

                if (existingItem != null)
                {
                    // Résoudre le conflit selon la stratégie configurée
                    switch (_settings.ConflictResolution)
                    {
                        case CartConflictResolution.AddQuantities:
                            existingItem.Quantity += guestItem.Quantity;
                            conflictResolutions.Add($"Added quantities for product {guestItem.ProductId}");
                            break;
                        case CartConflictResolution.KeepUserCart:
                            conflictResolutions.Add($"Kept user cart quantity for product {guestItem.ProductId}");
                            continue;
                        case CartConflictResolution.KeepGuestCart:
                            existingItem.Quantity = guestItem.Quantity;
                            existingItem.UnitPrice = guestItem.UnitPrice;
                            conflictResolutions.Add($"Replaced with guest cart for product {guestItem.ProductId}");
                            break;
                        case CartConflictResolution.KeepHigherQuantity:
                            if (guestItem.Quantity > existingItem.Quantity)
                            {
                                existingItem.Quantity = guestItem.Quantity;
                                existingItem.UnitPrice = guestItem.UnitPrice;
                            }
                            conflictResolutions.Add($"Kept higher quantity for product {guestItem.ProductId}");
                            break;
                    }
                }
                else
                {
                    // Ajouter le nouvel article
                    userCart.Items.Add(new CartItem
                    {
                        ProductId = guestItem.ProductId,
                        ProductName = guestItem.ProductName,
                        UnitPrice = guestItem.UnitPrice,
                        Quantity = guestItem.Quantity,
                        VariantId = guestItem.VariantId,
                        VariantName = guestItem.VariantName,
                        ImageUrl = guestItem.ImageUrl,
                        AddedAt = DateTime.UtcNow
                    });
                }
                mergedItems++;
            }

            // Sauvegarder le panier fusionné
            await SaveCartAsync(userCart, false);

            // Nettoyer le panier invité
            await ClearCartAsync(guestUserId, true);

            _logger.LogInformation("Cart merge completed: {MergedItems} items merged with {ConflictCount} conflicts resolved", 
                mergedItems, conflictResolutions.Count);

            return mergedItems > 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error merging guest cart {GuestUserId} to user cart {AuthUserId}", 
                guestUserId, authenticatedUserId);
            return false;
        }
    }

    public async Task<bool> TransferCartAsync(string fromUserId, string toUserId)
    {
        try
        {
            var sourceCart = await GetCartAsync(fromUserId);
            if (sourceCart?.Items?.Any() != true)
                return false;

            var targetCart = await GetCartAsync(toUserId);
            
            // Transférer tous les articles
            foreach (var item in sourceCart.Items)
            {
                targetCart.Items.Add(new CartItem
                {
                    ProductId = item.ProductId,
                    ProductName = item.ProductName,
                    UnitPrice = item.UnitPrice,
                    Quantity = item.Quantity,
                    VariantId = item.VariantId,
                    VariantName = item.VariantName,
                    ImageUrl = item.ImageUrl,
                    AddedAt = DateTime.UtcNow
                });
            }

            await SaveCartAsync(targetCart);
            await ClearCartAsync(fromUserId);

            _logger.LogInformation("Cart transferred from {FromUserId} to {ToUserId} with {ItemCount} items", 
                fromUserId, toUserId, sourceCart.Items.Count);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error transferring cart from {FromUserId} to {ToUserId}", fromUserId, toUserId);
            return false;
        }
    }

    public async Task<bool> ClearCartAsync(string userId, bool isGuest = false)
    {
        try
        {
            // Supprimer du cache
            await _cache.RemoveAsync(GetCacheKey(userId));

            // Supprimer de la base de données (seulement pour les utilisateurs authentifiés)
            if (!isGuest && !userId.StartsWith("guest_"))
            {
                var dbCart = await _dbContext.ShoppingCarts
                    .Include(c => c.Items)
                    .FirstOrDefaultAsync(c => c.UserId == userId);

                if (dbCart != null)
                {
                    _dbContext.ShoppingCarts.Remove(dbCart);
                    await _dbContext.SaveChangesAsync();
                }
            }

            _logger.LogDebug("Cart cleared for user {UserId} (Guest: {IsGuest})", userId, isGuest);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error clearing cart for user {UserId}", userId);
            return false;
        }
    }

    public async Task<bool> BackupCartAsync(string userId)
    {
        try
        {
            var cart = await GetCartAsync(userId);
            if (cart?.Items?.Any() != true)
                return false;

            var backupKey = GetBackupCacheKey(userId);
            var options = new DistributedCacheEntryOptions
            {
                AbsoluteExpirationRelativeToNow = TimeSpan.FromDays(_settings.BackupRetentionDays)
            };

            await _cache.SetStringAsync(backupKey, JsonSerializer.Serialize(cart), options);
            
            _logger.LogDebug("Cart backup created for user {UserId}", userId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error backing up cart for user {UserId}", userId);
            return false;
        }
    }

    public async Task<ShoppingCart?> RestoreCartAsync(string userId)
    {
        try
        {
            var backupKey = GetBackupCacheKey(userId);
            var backupData = await _cache.GetStringAsync(backupKey);

            if (string.IsNullOrEmpty(backupData))
                return null;

            var cart = JsonSerializer.Deserialize<ShoppingCart>(backupData);
            if (cart != null)
            {
                await SaveCartAsync(cart);
                _logger.LogInformation("Cart restored from backup for user {UserId}", userId);
            }

            return cart;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error restoring cart backup for user {UserId}", userId);
            return null;
        }
    }

    public async Task<List<ShoppingCart>> GetExpiredGuestCartsAsync(TimeSpan expiration)
    {
        try
        {
            var cutoffDate = DateTime.UtcNow - expiration;
            var expiredCarts = await _dbContext.ShoppingCarts
                .Where(c => c.UserId.StartsWith("guest_") && c.UpdatedAt < cutoffDate)
                .Include(c => c.Items)
                .ToListAsync();

            return expiredCarts;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting expired guest carts");
            return new List<ShoppingCart>();
        }
    }

    public async Task<bool> CleanupExpiredCartsAsync()
    {
        try
        {
            var expiredCarts = await GetExpiredGuestCartsAsync(TimeSpan.FromDays(_settings.GuestCartExpirationDays));
            
            if (expiredCarts.Any())
            {
                _dbContext.ShoppingCarts.RemoveRange(expiredCarts);
                await _dbContext.SaveChangesAsync();

                // Nettoyer aussi le cache
                foreach (var cart in expiredCarts)
                {
                    await _cache.RemoveAsync(GetCacheKey(cart.UserId));
                }

                _logger.LogInformation("Cleaned up {Count} expired guest carts", expiredCarts.Count);
            }

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error cleaning up expired carts");
            return false;
        }
    }

    public async Task<CartPersistenceStats> GetPersistenceStatsAsync()
    {
        try
        {
            var totalCarts = await _dbContext.ShoppingCarts.CountAsync();
            var guestCarts = await _dbContext.ShoppingCarts.CountAsync(c => c.UserId.StartsWith("guest_"));
            var userCarts = totalCarts - guestCarts;
            var cartsWithItems = await _dbContext.ShoppingCarts.CountAsync(c => c.Items.Any());
            var averageItemsPerCart = await _dbContext.ShoppingCarts
                .Where(c => c.Items.Any())
                .AverageAsync(c => c.Items.Count());

            return new CartPersistenceStats
            {
                TotalCarts = totalCarts,
                GuestCarts = guestCarts,
                UserCarts = userCarts,
                CartsWithItems = cartsWithItems,
                EmptyCarts = totalCarts - cartsWithItems,
                AverageItemsPerCart = averageItemsPerCart,
                LastUpdated = DateTime.UtcNow
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting persistence stats");
            return new CartPersistenceStats();
        }
    }

    // Méthodes privées
    private async Task<ShoppingCart?> GetCartFromCacheAsync(string userId)
    {
        try
        {
            var cacheKey = GetCacheKey(userId);
            var cachedData = await _cache.GetStringAsync(cacheKey);
            
            if (string.IsNullOrEmpty(cachedData))
                return null;

            return JsonSerializer.Deserialize<ShoppingCart>(cachedData);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error getting cart from cache for user {UserId}", userId);
            return null;
        }
    }

    private async Task SaveCartToCacheAsync(ShoppingCart cart)
    {
        try
        {
            var cacheKey = GetCacheKey(cart.UserId);
            var options = new DistributedCacheEntryOptions
            {
                SlidingExpiration = TimeSpan.FromHours(_settings.CacheExpirationHours),
                AbsoluteExpirationRelativeToNow = TimeSpan.FromDays(_settings.CacheAbsoluteExpirationDays)
            };

            var serializedCart = JsonSerializer.Serialize(cart);
            await _cache.SetStringAsync(cacheKey, serializedCart, options);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error saving cart to cache for user {UserId}", cart.UserId);
            throw;
        }
    }

    private async Task<ShoppingCart?> GetCartFromDatabaseAsync(string userId)
    {
        try
        {
            return await _dbContext.ShoppingCarts
                .Include(c => c.Items)
                .FirstOrDefaultAsync(c => c.UserId == userId);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error getting cart from database for user {UserId}", userId);
            return null;
        }
    }

    private async Task SaveCartToDatabaseAsync(ShoppingCart cart)
    {
        try
        {
            var existingCart = await _dbContext.ShoppingCarts
                .Include(c => c.Items)
                .FirstOrDefaultAsync(c => c.UserId == cart.UserId);

            if (existingCart != null)
            {
                // Mettre à jour le panier existant
                existingCart.UpdatedAt = cart.UpdatedAt;
                existingCart.Items.Clear();
                
                foreach (var item in cart.Items)
                {
                    existingCart.Items.Add(item);
                }
            }
            else
            {
                // Créer un nouveau panier
                _dbContext.ShoppingCarts.Add(cart);
            }

            await _dbContext.SaveChangesAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error saving cart to database for user {UserId}", cart.UserId);
            throw;
        }
    }

    private string GetCacheKey(string userId) => $"cart:{userId}";
    private string GetBackupCacheKey(string userId) => $"cart:backup:{userId}";
}

// Classes de configuration et statistiques
public class CartPersistenceSettings
{
    public int CacheExpirationHours { get; set; } = 24;
    public int CacheAbsoluteExpirationDays { get; set; } = 7;
    public int GuestCartExpirationDays { get; set; } = 30;
    public int BackupRetentionDays { get; set; } = 7;
    public CartConflictResolution ConflictResolution { get; set; } = CartConflictResolution.AddQuantities;
}

public enum CartConflictResolution
{
    AddQuantities,
    KeepUserCart,
    KeepGuestCart,
    KeepHigherQuantity
}

public class CartPersistenceStats
{
    public int TotalCarts { get; set; }
    public int GuestCarts { get; set; }
    public int UserCarts { get; set; }
    public int CartsWithItems { get; set; }
    public int EmptyCarts { get; set; }
    public double AverageItemsPerCart { get; set; }
    public DateTime LastUpdated { get; set; }
}
