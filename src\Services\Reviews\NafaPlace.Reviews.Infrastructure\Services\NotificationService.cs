using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using NafaPlace.Reviews.Application.Interfaces;
using System.Text.Json;

namespace NafaPlace.Reviews.Infrastructure.Services;

public class NotificationService : INotificationService
{
    private readonly ILogger<NotificationService> _logger;
    private readonly HttpClient _httpClient;
    private readonly string _notificationServiceUrl;

    public NotificationService(
        ILogger<NotificationService> logger,
        HttpClient httpClient,
        IConfiguration configuration)
    {
        _logger = logger;
        _httpClient = httpClient;
        _notificationServiceUrl = configuration.GetValue<string>("NotificationServiceUrl") ?? "http://localhost:5007";
    }
    
    public async Task SendNewReviewNotificationAsync(int reviewId, int productId, string userId)
    {
        try
        {
            var notification = new
            {
                UserId = userId,
                Title = "Nouvel avis publié",
                Message = $"Votre avis pour le produit #{productId} a été publié avec succès.",
                Type = "review_published",
                Data = new { ReviewId = reviewId, ProductId = productId },
                Priority = "normal",
                ActionUrl = $"/products/{productId}#review-{reviewId}"
            };

            await SendNotificationAsync(notification);
            _logger.LogInformation("✅ New review notification sent: Review #{ReviewId} for product #{ProductId} by user {UserId}",
                reviewId, productId, userId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ Failed to send new review notification for review #{ReviewId}", reviewId);
        }
    }
    
    public async Task SendReviewReplyNotificationAsync(int replyId, int reviewId, string reviewUserId)
    {
        try
        {
            var notification = new
            {
                UserId = reviewUserId,
                Title = "Nouvelle réponse à votre avis",
                Message = $"Une réponse a été ajoutée à votre avis #{reviewId}.",
                Type = "review_reply",
                Data = new { ReplyId = replyId, ReviewId = reviewId },
                Priority = "normal",
                ActionUrl = $"/reviews/{reviewId}#reply-{replyId}"
            };

            await SendNotificationAsync(notification);
            _logger.LogInformation("✅ Review reply notification sent: Reply #{ReplyId} for review #{ReviewId} to user {UserId}",
                replyId, reviewId, reviewUserId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ Failed to send review reply notification for reply #{ReplyId}", replyId);
        }
    }

    public async Task SendReviewApprovedNotificationAsync(int reviewId, string userId)
    {
        try
        {
            var notification = new
            {
                UserId = userId,
                Title = "Avis approuvé",
                Message = $"Votre avis #{reviewId} a été approuvé et est maintenant visible publiquement.",
                Type = "review_approved",
                Data = new { ReviewId = reviewId },
                Priority = "normal",
                ActionUrl = $"/reviews/{reviewId}"
            };

            await SendNotificationAsync(notification);
            _logger.LogInformation("✅ Review approved notification sent: Review #{ReviewId} for user {UserId}", reviewId, userId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ Failed to send review approved notification for review #{ReviewId}", reviewId);
        }
    }

    public async Task SendReviewRejectedNotificationAsync(int reviewId, string userId, string? reason = null)
    {
        try
        {
            var notification = new
            {
                UserId = userId,
                Title = "Avis rejeté",
                Message = $"Votre avis #{reviewId} a été rejeté." + (string.IsNullOrEmpty(reason) ? "" : $" Raison: {reason}"),
                Type = "review_rejected",
                Data = new { ReviewId = reviewId, Reason = reason },
                Priority = "high",
                ActionUrl = $"/reviews/my-reviews"
            };

            await SendNotificationAsync(notification);
            _logger.LogInformation("✅ Review rejected notification sent: Review #{ReviewId} for user {UserId} with reason: {Reason}",
                reviewId, userId, reason ?? "No reason provided");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ Failed to send review rejected notification for review #{ReviewId}", reviewId);
        }
    }

    private async Task SendNotificationAsync(object notification)
    {
        try
        {
            var json = JsonSerializer.Serialize(notification);
            var content = new StringContent(json, System.Text.Encoding.UTF8, "application/json");

            var response = await _httpClient.PostAsync($"{_notificationServiceUrl}/api/notifications", content);

            if (!response.IsSuccessStatusCode)
            {
                var errorContent = await response.Content.ReadAsStringAsync();
                _logger.LogWarning("Notification service returned {StatusCode}: {Error}", response.StatusCode, errorContent);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send notification to notification service");
        }
    }
}
