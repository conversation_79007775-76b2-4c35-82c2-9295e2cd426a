namespace NafaPlace.Payment.API.Models
{
    public enum OrangeMoneyStatus
    {
        Pending,
        Success,
        Failed,
        Expired,
        Cancelled
    }

    public class OrangeMoneyPaymentRequest
    {
        public decimal Amount { get; set; }
        public string Currency { get; set; } = "GNF";
        public string PhoneNumber { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string OrderId { get; set; } = string.Empty;
        public string CallbackUrl { get; set; } = string.Empty;
        public string ReturnUrl { get; set; } = string.Empty;
    }

    public class OrangeMoneyPaymentResponse
    {
        public string TransactionId { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public string PaymentUrl { get; set; } = string.Empty;
        public string QrCode { get; set; } = string.Empty;
        public int ExpiresInMinutes { get; set; } = 15;
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    }

    public class OrangeMoneyStatusResponse
    {
        public string TransactionId { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public decimal Amount { get; set; }
        public string Currency { get; set; } = string.Empty;
        public string PhoneNumber { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; }
        public DateTime? CompletedAt { get; set; }
        public string? FailureReason { get; set; }
    }

    public class OrangeMoneyApiResponse
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public object? Data { get; set; }
        public string ErrorCode { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
        public string PaymentUrl { get; set; } = string.Empty;
        public string TransactionId { get; set; } = string.Empty;
        public string QrCode { get; set; } = string.Empty;
        public DateTime? ExpiresAt { get; set; }
    }

    public class OrangeMoneyErrorResponse
    {
        public string ErrorCode { get; set; } = string.Empty;
        public string ErrorMessage { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public string Details { get; set; } = string.Empty;
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    }

    public class OrangeMoneyCallback
    {
        public string TransactionId { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
        public string Signature { get; set; } = string.Empty;
        public string Amount { get; set; } = string.Empty;
        public string Currency { get; set; } = string.Empty;
        public DateTime Timestamp { get; set; }
    }
}
