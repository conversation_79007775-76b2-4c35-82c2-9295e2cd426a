@using Microsoft.AspNetCore.Components.Forms
@inject IJSRuntime JSRuntime

<div class="image-uploader">
    <div class="upload-area @(_isDragOver ? "drag-over" : "")" 
         @ondrop="HandleDrop" 
         @ondragover="HandleDragOver" 
         @ondragenter="HandleDragEnter" 
         @ondragleave="HandleDragLeave"
         @onclick="OpenFileDialog">
        
        <InputFile @ref="fileInput" 
                   multiple="@AllowMultiple" 
                   accept="@AcceptedTypes" 
                   @onchange="HandleFileSelection" 
                   style="display: none;" />
        
        <div class="upload-content">
            <i class="fas fa-cloud-upload-alt upload-icon"></i>
            <h5>Glissez vos images ici</h5>
            <p>ou <span class="text-primary">cliquez pour parcourir</span></p>
            <small class="text-muted">
                Formats acceptés: JPG, PNG, GIF, WEBP (max @(MaxFileSizeMB)MB par image)
            </small>
        </div>
    </div>

    @if (_uploadedImages.Any())
    {
        <div class="uploaded-images mt-3">
            <h6>Images sélectionnées (@_uploadedImages.Count)</h6>
            <div class="images-grid">
                @foreach (var image in _uploadedImages)
                {
                    <div class="image-item @(image.IsMain ? "main-image" : "")">
                        <div class="image-preview">
                            <img src="@image.PreviewUrl" alt="@image.FileName" />
                            
                            @if (_isUploading && image.UploadProgress < 100)
                            {
                                <div class="upload-overlay">
                                    <div class="progress">
                                        <div class="progress-bar" style="width: @(image.UploadProgress)%"></div>
                                    </div>
                                    <small>@(image.UploadProgress)%</small>
                                </div>
                            }
                            
                            @if (image.HasError)
                            {
                                <div class="error-overlay">
                                    <i class="fas fa-exclamation-triangle"></i>
                                    <small>@image.ErrorMessage</small>
                                </div>
                            }
                            
                            @if (image.IsUploaded)
                            {
                                <div class="success-overlay">
                                    <i class="fas fa-check-circle"></i>
                                </div>
                            }
                        </div>
                        
                        <div class="image-actions">
                            <button type="button" class="btn btn-sm btn-outline-primary" 
                                    @onclick="() => SetAsMain(image)"
                                    disabled="@(_isUploading || image.IsMain)">
                                @if (image.IsMain)
                                {
                                    <i class="fas fa-star"></i> Principale
                                }
                                else
                                {
                                    <i class="far fa-star"></i> Définir comme principale
                                }
                            </button>
                            
                            <button type="button" class="btn btn-sm btn-outline-danger" 
                                    @onclick="() => RemoveImage(image)"
                                    disabled="@_isUploading">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                        
                        <div class="image-info">
                            <small class="text-muted">@image.FileName</small>
                            <small class="text-muted">(@(image.FileSizeKB) KB)</small>
                        </div>
                    </div>
                }
            </div>
        </div>
    }

    @if (_validationErrors.Any())
    {
        <div class="alert alert-warning mt-3">
            <h6><i class="fas fa-exclamation-triangle"></i> Erreurs de validation:</h6>
            <ul class="mb-0">
                @foreach (var error in _validationErrors)
                {
                    <li>@error</li>
                }
            </ul>
        </div>
    }

    <div class="upload-controls mt-3">
        <button type="button" class="btn btn-primary" 
                @onclick="StartUpload" 
                disabled="@(_isUploading || !_uploadedImages.Any() || _uploadedImages.All(i => i.IsUploaded))">
            @if (_isUploading)
            {
                <span class="spinner-border spinner-border-sm me-2"></span>
                Upload en cours...
            }
            else
            {
                <i class="fas fa-upload me-2"></i>
                Uploader les images (@_uploadedImages.Count(i => !i.IsUploaded))
            }
        </button>
        
        <button type="button" class="btn btn-outline-secondary ms-2" 
                @onclick="ClearAll" 
                disabled="@_isUploading">
            <i class="fas fa-times me-2"></i>
            Tout effacer
        </button>
    </div>
</div>

@code {
    [Parameter] public bool AllowMultiple { get; set; } = true;
    [Parameter] public int MaxFileSizeMB { get; set; } = 5;
    [Parameter] public string AcceptedTypes { get; set; } = "image/*";
    [Parameter] public EventCallback<List<UploadedImageInfo>> OnImagesUploaded { get; set; }
    [Parameter] public EventCallback<List<UploadedImageInfo>> OnImagesChanged { get; set; }

    private InputFile? fileInput;
    private List<UploadedImageInfo> _uploadedImages = new();
    private List<string> _validationErrors = new();
    private bool _isDragOver = false;
    private bool _isUploading = false;

    private long MaxFileSize => MaxFileSizeMB * 1024 * 1024;

    private async Task HandleFileSelection(InputFileChangeEventArgs e)
    {
        await ProcessFiles(e.GetMultipleFiles());
    }

    private async Task HandleDrop(DragEventArgs e)
    {
        _isDragOver = false;
        
        // Note: Pour une vraie implémentation, vous devriez utiliser JavaScript
        // pour récupérer les fichiers depuis l'événement drop
        await JSRuntime.InvokeVoidAsync("console.log", "Files dropped");
    }

    private void HandleDragOver(DragEventArgs e) => _isDragOver = true;
    private void HandleDragEnter(DragEventArgs e) => _isDragOver = true;
    private void HandleDragLeave(DragEventArgs e) => _isDragOver = false;

    private async Task OpenFileDialog()
    {
        if (fileInput != null)
        {
            await JSRuntime.InvokeVoidAsync("triggerFileInput", fileInput.Element);
        }
    }

    private async Task ProcessFiles(IReadOnlyList<IBrowserFile> files)
    {
        _validationErrors.Clear();
        
        foreach (var file in files)
        {
            if (!ValidateFile(file))
                continue;

            try
            {
                var imageInfo = new UploadedImageInfo
                {
                    Id = Guid.NewGuid().ToString(),
                    FileName = file.Name,
                    FileSizeKB = (int)(file.Size / 1024),
                    ContentType = file.ContentType,
                    IsMain = !_uploadedImages.Any() // Premier fichier = image principale
                };

                // Créer une prévisualisation
                var buffer = new byte[file.Size];
                await file.OpenReadStream(MaxFileSize).ReadAsync(buffer);
                imageInfo.Base64Data = Convert.ToBase64String(buffer);
                imageInfo.PreviewUrl = $"data:{file.ContentType};base64,{imageInfo.Base64Data}";

                _uploadedImages.Add(imageInfo);
            }
            catch (Exception ex)
            {
                _validationErrors.Add($"Erreur lors du traitement de {file.Name}: {ex.Message}");
            }
        }

        await OnImagesChanged.InvokeAsync(_uploadedImages);
        StateHasChanged();
    }

    private bool ValidateFile(IBrowserFile file)
    {
        if (file.Size > MaxFileSize)
        {
            _validationErrors.Add($"{file.Name}: Taille trop importante (max {MaxFileSizeMB}MB)");
            return false;
        }

        var allowedTypes = new[] { "image/jpeg", "image/jpg", "image/png", "image/gif", "image/webp" };
        if (!allowedTypes.Contains(file.ContentType.ToLower()))
        {
            _validationErrors.Add($"{file.Name}: Type de fichier non supporté");
            return false;
        }

        return true;
    }

    private void SetAsMain(UploadedImageInfo image)
    {
        foreach (var img in _uploadedImages)
        {
            img.IsMain = img.Id == image.Id;
        }
        StateHasChanged();
    }

    private async Task RemoveImage(UploadedImageInfo image)
    {
        _uploadedImages.Remove(image);
        
        // Si c'était l'image principale et qu'il reste des images, définir la première comme principale
        if (image.IsMain && _uploadedImages.Any())
        {
            _uploadedImages.First().IsMain = true;
        }

        await OnImagesChanged.InvokeAsync(_uploadedImages);
        StateHasChanged();
    }

    private async Task StartUpload()
    {
        _isUploading = true;
        var imagesToUpload = _uploadedImages.Where(i => !i.IsUploaded).ToList();

        foreach (var image in imagesToUpload)
        {
            try
            {
                image.UploadProgress = 0;
                StateHasChanged();

                // Simuler le progrès d'upload
                for (int i = 0; i <= 90; i += 10)
                {
                    image.UploadProgress = i;
                    StateHasChanged();
                    await Task.Delay(100);
                }

                // Ici, vous feriez l'appel API réel
                await Task.Delay(500); // Simuler l'upload

                image.UploadProgress = 100;
                image.IsUploaded = true;
                image.UploadedUrl = $"https://example.com/images/{image.Id}"; // URL simulée
            }
            catch (Exception ex)
            {
                image.HasError = true;
                image.ErrorMessage = ex.Message;
            }
        }

        _isUploading = false;
        await OnImagesUploaded.InvokeAsync(_uploadedImages.Where(i => i.IsUploaded).ToList());
        StateHasChanged();
    }

    private async Task ClearAll()
    {
        _uploadedImages.Clear();
        _validationErrors.Clear();
        await OnImagesChanged.InvokeAsync(_uploadedImages);
        StateHasChanged();
    }

    public class UploadedImageInfo
    {
        public string Id { get; set; } = string.Empty;
        public string FileName { get; set; } = string.Empty;
        public int FileSizeKB { get; set; }
        public string ContentType { get; set; } = string.Empty;
        public string Base64Data { get; set; } = string.Empty;
        public string PreviewUrl { get; set; } = string.Empty;
        public string? UploadedUrl { get; set; }
        public bool IsMain { get; set; }
        public bool IsUploaded { get; set; }
        public bool HasError { get; set; }
        public string? ErrorMessage { get; set; }
        public int UploadProgress { get; set; }
    }
}

<style>
    .image-uploader {
        width: 100%;
    }

    .upload-area {
        border: 2px dashed #dee2e6;
        border-radius: 8px;
        padding: 40px 20px;
        text-align: center;
        cursor: pointer;
        transition: all 0.3s ease;
        background-color: #f8f9fa;
    }

    .upload-area:hover, .upload-area.drag-over {
        border-color: #007bff;
        background-color: #e3f2fd;
    }

    .upload-icon {
        font-size: 3rem;
        color: #6c757d;
        margin-bottom: 1rem;
    }

    .images-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
        gap: 1rem;
        margin-top: 1rem;
    }

    .image-item {
        border: 2px solid #dee2e6;
        border-radius: 8px;
        overflow: hidden;
        transition: all 0.3s ease;
    }

    .image-item.main-image {
        border-color: #ffc107;
        box-shadow: 0 0 0 2px rgba(255, 193, 7, 0.25);
    }

    .image-preview {
        position: relative;
        height: 150px;
        overflow: hidden;
    }

    .image-preview img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .upload-overlay, .error-overlay, .success-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        color: white;
    }

    .upload-overlay {
        background-color: rgba(0, 123, 255, 0.8);
    }

    .error-overlay {
        background-color: rgba(220, 53, 69, 0.8);
    }

    .success-overlay {
        background-color: rgba(40, 167, 69, 0.8);
    }

    .image-actions {
        padding: 8px;
        display: flex;
        gap: 4px;
    }

    .image-actions .btn {
        flex: 1;
        font-size: 0.8rem;
    }

    .image-info {
        padding: 8px;
        background-color: #f8f9fa;
        text-align: center;
    }

    .progress {
        width: 80%;
        height: 4px;
        margin-bottom: 8px;
    }
</style>

<script>
    window.triggerFileInput = (element) => {
        element.click();
    };
</script>
