using Microsoft.Extensions.Logging;
using NafaPlace.Notifications.Application.Services;

namespace NafaPlace.Notifications.Infrastructure.Services;

public class SmsService : ISmsService
{
    private readonly ILogger<SmsService> _logger;

    public SmsService(ILogger<SmsService> logger)
    {
        _logger = logger;
    }

    public async Task<bool> SendSmsAsync(string phoneNumber, string message)
    {
        try
        {
            // Valider le format du numéro guinéen
            var formattedNumber = FormatGuineanPhoneNumber(phoneNumber);
            if (string.IsNullOrEmpty(formattedNumber))
            {
                _logger.LogWarning("Invalid phone number format: {PhoneNumber}", phoneNumber);
                return false;
            }

            // Mode développement - simulation d'envoi de SMS
            _logger.LogInformation("📱 [SIMULATION] Sending SMS to {PhoneNumber}: {Message}", formattedNumber, message);

            // Simuler un délai réseau réaliste
            await Task.Delay(Random.Shared.Next(100, 300));

            // Simuler occasionnellement des échecs
            if (Random.Shared.Next(1, 100) <= 3) // 3% de chance d'échec
            {
                throw new Exception("Simulated SMS delivery failure");
            }

            _logger.LogInformation("✅ [SIMULATION] SMS sent successfully to {PhoneNumber}", formattedNumber);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ [SIMULATION] Failed to send SMS to {PhoneNumber}", phoneNumber);
            return false;
        }
    }

    public async Task<bool> SendSmsWithTemplateAsync(string phoneNumber, string templateCode, Dictionary<string, object> variables)
    {
        try
        {
            _logger.LogInformation("📱 Sending templated SMS to {PhoneNumber} with template {TemplateCode}", phoneNumber, templateCode);
            
            // Simulation d'envoi avec template
            await Task.Delay(100);
            
            _logger.LogInformation("✅ Templated SMS sent successfully to {PhoneNumber}", phoneNumber);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ Failed to send templated SMS to {PhoneNumber}", phoneNumber);
            return false;
        }
    }

    public async Task<bool> SendBulkSmsAsync(List<string> phoneNumbers, string message)
    {
        var successCount = 0;
        
        foreach (var phoneNumber in phoneNumbers)
        {
            if (await SendSmsAsync(phoneNumber, message))
            {
                successCount++;
            }
        }

        _logger.LogInformation("📱 Bulk SMS sent: {SuccessCount}/{TotalCount}", successCount, phoneNumbers.Count);
        return successCount == phoneNumbers.Count;
    }

    public async Task<bool> IsPhoneNumberValidAsync(string phoneNumber)
    {
        await Task.CompletedTask;
        
        // Validation basique de numéro de téléphone guinéen
        if (string.IsNullOrWhiteSpace(phoneNumber))
            return false;

        // Format guinéen : +224XXXXXXXX ou 224XXXXXXXX ou 6XXXXXXXX/7XXXXXXXX
        var cleanNumber = phoneNumber.Replace("+", "").Replace(" ", "").Replace("-", "");
        
        if (cleanNumber.StartsWith("224") && cleanNumber.Length == 12)
            return true;
        
        if ((cleanNumber.StartsWith("6") || cleanNumber.StartsWith("7")) && cleanNumber.Length == 9)
            return true;

        return false;
    }

    private string FormatGuineanPhoneNumber(string phoneNumber)
    {
        if (string.IsNullOrWhiteSpace(phoneNumber))
            return string.Empty;

        // Nettoyer le numéro
        var cleaned = phoneNumber.Replace(" ", "").Replace("-", "").Replace("(", "").Replace(")", "");

        // Formats acceptés pour la Guinée:
        // +224XXXXXXXXX (format international)
        // 224XXXXXXXXX (sans +)
        // 6XXXXXXXX ou 7XXXXXXXX (format local)

        if (cleaned.StartsWith("+224") && cleaned.Length == 13)
        {
            return cleaned; // Déjà au bon format
        }
        else if (cleaned.StartsWith("224") && cleaned.Length == 12)
        {
            return $"+{cleaned}";
        }
        else if ((cleaned.StartsWith("6") || cleaned.StartsWith("7")) && cleaned.Length == 9)
        {
            return $"+224{cleaned}";
        }

        return string.Empty; // Format non reconnu
    }
}
