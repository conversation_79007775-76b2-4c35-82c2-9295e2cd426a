namespace NafaPlace.Catalog.Infrastructure.Services;

public class ImageUploadResult
{
    public bool Success { get; set; }
    public string? PublicId { get; set; }
    public string? Url { get; set; }
    public int Width { get; set; }
    public int Height { get; set; }
    public string? Format { get; set; }
    public long Bytes { get; set; }
    public string? ErrorMessage { get; set; }
    public Dictionary<string, object>? Metadata { get; set; }
}

public class ImageUploadData
{
    public Stream ImageStream { get; set; } = null!;
    public string FileName { get; set; } = string.Empty;
    public ImageUploadOptions? Options { get; set; }
}

public class ImageUploadOptions
{
    public bool Overwrite { get; set; } = true;
    public string? Format { get; set; } = "jpg";
    public List<string>? Tags { get; set; }
    public ImageTransformation? Transformation { get; set; }
    public ImageOptimizationOptions? OptimizationOptions { get; set; }
}

public class ImageTransformation
{
    public int? Width { get; set; }
    public int? Height { get; set; }
    public string? Crop { get; set; } = "fill";
    public string? Gravity { get; set; } = "center";
    public int? Quality { get; set; } = 80;
    public string? Format { get; set; }
}

public class ImageOptimizationOptions
{
    public int? MaxWidth { get; set; } = 1920;
    public int? MaxHeight { get; set; } = 1080;
    public int Quality { get; set; } = 85;
    public bool PreserveAspectRatio { get; set; } = true;
}

public class ImageValidationResult
{
    public bool IsValid { get; set; }
    public string? ErrorMessage { get; set; }
    public Dictionary<string, object>? ValidationDetails { get; set; }
}

public class CloudinarySettings
{
    public string CloudName { get; set; } = string.Empty;
    public string ApiKey { get; set; } = string.Empty;
    public string ApiSecret { get; set; } = string.Empty;
    public long MaxFileSizeBytes { get; set; } = 10 * 1024 * 1024; // 10 MB
    public int MaxWidth { get; set; } = 4000;
    public int MaxHeight { get; set; } = 4000;
    public int MaxConcurrentUploads { get; set; } = 5;
    public List<string> AllowedExtensions { get; set; } = new() { ".jpg", ".jpeg", ".png", ".gif", ".webp" };
    public string DefaultFolder { get; set; } = "nafaplace";
    public bool AutoOptimize { get; set; } = true;
    public int DefaultQuality { get; set; } = 85;
}

// Modèles pour les requêtes API
public class BulkImageUploadRequest
{
    public int ProductId { get; set; }
    public List<ImageUploadItem> Images { get; set; } = new();
}

public class ImageUploadItem
{
    public string Image { get; set; } = string.Empty; // Base64
    public bool IsMain { get; set; }
    public string? AltText { get; set; }
    public int? DisplayOrder { get; set; }
    public Dictionary<string, string>? Metadata { get; set; }
}

public class BulkImageUploadResponse
{
    public bool Success { get; set; }
    public List<ImageUploadResult> Results { get; set; } = new();
    public List<string> Errors { get; set; } = new();
    public int SuccessCount { get; set; }
    public int ErrorCount { get; set; }
}

// Modèles pour la gestion des images produits
public class ProductImageManagementRequest
{
    public int ProductId { get; set; }
    public List<ProductImageAction> Actions { get; set; } = new();
}

public class ProductImageAction
{
    public string Action { get; set; } = string.Empty; // "add", "update", "delete", "reorder"
    public int? ImageId { get; set; }
    public string? Image { get; set; } // Base64 pour "add"
    public bool? IsMain { get; set; }
    public string? AltText { get; set; }
    public int? DisplayOrder { get; set; }
}

public class ProductImageManagementResponse
{
    public bool Success { get; set; }
    public List<ProductImageDto> Images { get; set; } = new();
    public List<string> Errors { get; set; } = new();
    public string? Message { get; set; }
}

public class ProductImageDto
{
    public int Id { get; set; }
    public int ProductId { get; set; }
    public string ImageUrl { get; set; } = string.Empty;
    public string? ThumbnailUrl { get; set; }
    public bool IsMain { get; set; }
    public string? AltText { get; set; }
    public int DisplayOrder { get; set; }
    public string? PublicId { get; set; }
    public DateTime CreatedAt { get; set; }
    public Dictionary<string, string>? Metadata { get; set; }
}

// Modèles pour les transformations d'images
public class ImageVariant
{
    public string Name { get; set; } = string.Empty;
    public int Width { get; set; }
    public int Height { get; set; }
    public string Crop { get; set; } = "fill";
    public int Quality { get; set; } = 80;
    public string? Format { get; set; }
}

public static class ImageVariants
{
    public static readonly ImageVariant Thumbnail = new()
    {
        Name = "thumbnail",
        Width = 150,
        Height = 150,
        Quality = 70
    };

    public static readonly ImageVariant Small = new()
    {
        Name = "small",
        Width = 300,
        Height = 300,
        Quality = 75
    };

    public static readonly ImageVariant Medium = new()
    {
        Name = "medium",
        Width = 600,
        Height = 600,
        Quality = 80
    };

    public static readonly ImageVariant Large = new()
    {
        Name = "large",
        Width = 1200,
        Height = 1200,
        Quality = 85
    };

    public static readonly ImageVariant Hero = new()
    {
        Name = "hero",
        Width = 1920,
        Height = 1080,
        Quality = 90
    };

    public static List<ImageVariant> GetAllVariants() => new()
    {
        Thumbnail, Small, Medium, Large, Hero
    };
}

// Modèles pour les statistiques d'images
public class ImageStatistics
{
    public int TotalImages { get; set; }
    public long TotalSizeBytes { get; set; }
    public Dictionary<string, int> ImagesByFormat { get; set; } = new();
    public Dictionary<string, int> ImagesByFolder { get; set; } = new();
    public DateTime LastUpload { get; set; }
    public List<string> RecentUploads { get; set; } = new();
}

// Interface pour les services d'images
public interface IImageManagementService
{
    Task<BulkImageUploadResponse> UploadProductImagesAsync(BulkImageUploadRequest request);
    Task<ProductImageManagementResponse> ManageProductImagesAsync(ProductImageManagementRequest request);
    Task<List<ProductImageDto>> GetProductImagesAsync(int productId);
    Task<bool> DeleteProductImageAsync(int imageId);
    Task<bool> SetMainImageAsync(int productId, int imageId);
    Task<bool> ReorderImagesAsync(int productId, List<int> imageIds);
    Task<ImageStatistics> GetImageStatisticsAsync();
    Task<List<string>> GenerateImageVariantsAsync(string publicId, List<ImageVariant> variants);
}
