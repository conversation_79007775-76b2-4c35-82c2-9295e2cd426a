@page "/payment/orange-money-simulator"
@using System.Text.Json
@inject IJSRuntime JSRuntime
@inject NavigationManager NavigationManager
@inject HttpClient HttpClient

<PageTitle>Simulateur Orange Money - NafaPlace</PageTitle>

<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="card shadow">
                <div class="card-header bg-warning text-dark text-center">
                    <h4 class="mb-0">
                        <i class="fas fa-mobile-alt me-2"></i>
                        Simulateur Orange Money
                    </h4>
                    <small class="text-muted">Mode Développement</small>
                </div>
                <div class="card-body">
                    <div class="text-center mb-4">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            Ceci est une simulation pour les tests en développement
                        </div>
                    </div>

                    @if (!string.IsNullOrEmpty(_errorMessage))
                    {
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            @_errorMessage
                        </div>
                    }

                    <div class="payment-details mb-4">
                        <h5>Détails du Paiement</h5>
                        <div class="row">
                            <div class="col-6">
                                <strong>Montant:</strong>
                            </div>
                            <div class="col-6 text-end">
                                @_amount.ToString("N0") GNF
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-6">
                                <strong>Téléphone:</strong>
                            </div>
                            <div class="col-6 text-end">
                                @_phoneNumber
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-6">
                                <strong>Commande:</strong>
                            </div>
                            <div class="col-6 text-end">
                                #@_orderId
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-6">
                                <strong>Transaction:</strong>
                            </div>
                            <div class="col-6 text-end">
                                @_transactionId
                            </div>
                        </div>
                    </div>

                    <div class="simulation-actions">
                        <h5>Actions de Simulation</h5>
                        <div class="d-grid gap-2">
                            <button class="btn btn-success btn-lg" 
                                    @onclick="() => SimulatePayment('SUCCESS')"
                                    disabled="@_processing">
                                <i class="fas fa-check-circle me-2"></i>
                                Simuler Paiement Réussi
                            </button>
                            
                            <button class="btn btn-danger btn-lg" 
                                    @onclick="() => SimulatePayment('FAILED')"
                                    disabled="@_processing">
                                <i class="fas fa-times-circle me-2"></i>
                                Simuler Paiement Échoué
                            </button>
                            
                            <button class="btn btn-warning btn-lg" 
                                    @onclick="() => SimulatePayment('CANCELLED')"
                                    disabled="@_processing">
                                <i class="fas fa-ban me-2"></i>
                                Simuler Paiement Annulé
                            </button>
                        </div>
                    </div>

                    @if (_processing)
                    {
                        <div class="text-center mt-3">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Traitement...</span>
                            </div>
                            <p class="mt-2">Traitement en cours...</p>
                        </div>
                    }
                </div>
                <div class="card-footer text-center">
                    <small class="text-muted">
                        <i class="fas fa-shield-alt me-1"></i>
                        Environnement de développement sécurisé
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

@code {
    private string _amount = "0";
    private string _phoneNumber = "";
    private string _orderId = "";
    private string _transactionId = "";
    private string _errorMessage = "";
    private bool _processing = false;

    protected override async Task OnInitializedAsync()
    {
        // Récupérer les paramètres de l'URL
        var uri = new Uri(NavigationManager.Uri);
        var query = System.Web.HttpUtility.ParseQueryString(uri.Query);
        
        _amount = query["amount"] ?? "0";
        _phoneNumber = query["phone"] ?? "";
        _orderId = query["order"] ?? "";
        _transactionId = query["tx"] ?? "";

        if (string.IsNullOrEmpty(_orderId) || string.IsNullOrEmpty(_transactionId))
        {
            _errorMessage = "Paramètres de paiement manquants";
        }
    }

    private async Task SimulatePayment(string status)
    {
        if (_processing) return;

        _processing = true;
        _errorMessage = "";

        try
        {
            // Appeler l'API de simulation
            var simulationRequest = new
            {
                TransactionId = _transactionId,
                Status = status,
                OrderId = _orderId
            };

            var response = await HttpClient.PostAsJsonAsync(
                "http://localhost:5005/api/orangemoney/simulate-payment", 
                simulationRequest);

            if (response.IsSuccessStatusCode)
            {
                await JSRuntime.InvokeVoidAsync("showToast", 
                    $"Paiement simulé: {GetStatusText(status)}", "success");

                // Rediriger selon le statut
                var redirectUrl = status switch
                {
                    "SUCCESS" => $"/payment/success?orderId={_orderId}",
                    "FAILED" => $"/payment/failed?orderId={_orderId}",
                    "CANCELLED" => $"/payment/cancel?orderId={_orderId}",
                    _ => $"/payment/cancel?orderId={_orderId}"
                };

                await Task.Delay(1500); // Petit délai pour voir le message
                NavigationManager.NavigateTo(redirectUrl);
            }
            else
            {
                _errorMessage = "Erreur lors de la simulation du paiement";
            }
        }
        catch (Exception ex)
        {
            _errorMessage = $"Erreur: {ex.Message}";
        }
        finally
        {
            _processing = false;
        }
    }

    private string GetStatusText(string status) => status switch
    {
        "SUCCESS" => "Réussi",
        "FAILED" => "Échoué", 
        "CANCELLED" => "Annulé",
        _ => "Inconnu"
    };
}

<style>
    .payment-details {
        background-color: #f8f9fa;
        padding: 1rem;
        border-radius: 0.5rem;
        border-left: 4px solid #007bff;
    }

    .simulation-actions {
        background-color: #fff3cd;
        padding: 1rem;
        border-radius: 0.5rem;
        border-left: 4px solid #ffc107;
    }

    .btn-lg {
        padding: 0.75rem 1.5rem;
        font-size: 1.1rem;
    }

    .card {
        border: none;
        border-radius: 1rem;
    }

    .card-header {
        border-radius: 1rem 1rem 0 0 !important;
    }
</style>
